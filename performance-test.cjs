// Performance test for different precision loading methods
const https = require('https');
const fs = require('fs');

// Test 1: API Call Performance
async function testAPIPerformance() {
    console.log('🔍 Testing API Performance...');
    const startTime = performance.now();
    
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'fapi.binance.com',
            path: '/fapi/v1/exchangeInfo',
            method: 'GET'
        };

        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const parsed = JSON.parse(data);
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    console.log(`✅ API Call: ${duration.toFixed(2)}ms`);
                    console.log(`📊 Symbols loaded: ${parsed.symbols?.length || 0}`);
                    resolve({ duration, symbolCount: parsed.symbols?.length || 0 });
                } catch (error) {
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.end();
    });
}

// Test 2: File Read Performance
function testFilePerformance() {
    console.log('\n🔍 Testing File Performance...');
    const startTime = performance.now();
    
    try {
        // Simulate reading a cached file
        const data = fs.readFileSync('./trading_rules.json', 'utf8');
        const parsed = JSON.parse(data);
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        console.log(`✅ File Read: ${duration.toFixed(2)}ms`);
        console.log(`📊 Rules loaded: ${parsed.length || 0}`);
        return { duration, ruleCount: parsed.length || 0 };
    } catch (error) {
        console.error('❌ File read error:', error.message);
        return { duration: 0, ruleCount: 0 };
    }
}

// Test 3: RAM Access Performance
function testRAMPerformance() {
    console.log('\n🔍 Testing RAM Performance...');
    
    // Simulate RAM cache
    const ramCache = new Map();
    
    // Pre-populate cache
    const populateStart = performance.now();
    for (let i = 0; i < 1000; i++) {
        ramCache.set(`SYMBOL${i}USDT`, {
            lotSize: 0.001,
            precision: 3
        });
    }
    const populateEnd = performance.now();
    const populateDuration = populateEnd - populateStart;
    
    // Test access speed
    const accessStart = performance.now();
    for (let i = 0; i < 1000; i++) {
        const result = ramCache.get(`SYMBOL${i}USDT`);
    }
    const accessEnd = performance.now();
    const accessDuration = accessEnd - accessStart;
    
    console.log(`✅ RAM Populate: ${populateDuration.toFixed(2)}ms`);
    console.log(`✅ RAM Access (1000 lookups): ${accessDuration.toFixed(4)}ms`);
    console.log(`⚡ Average per lookup: ${(accessDuration / 1000).toFixed(6)}ms`);
    
    return { 
        populateDuration, 
        accessDuration, 
        avgPerLookup: accessDuration / 1000,
        cacheSize: ramCache.size 
    };
}

// Test 4: Quantity Calculation Performance
function testQuantityCalculation() {
    console.log('\n🔍 Testing Quantity Calculation Performance...');
    
    const testCases = [
        { symbol: 'BTCUSDT', price: 43000, amount: 0.3, leverage: 20 },
        { symbol: 'ETHUSDT', price: 2500, amount: 0.3, leverage: 20 },
        { symbol: 'SKATEUSDT', price: 0.26935, amount: 0.3, leverage: 20 },
        { symbol: 'DOGEUSDT', price: 0.08, amount: 0.3, leverage: 20 }
    ];
    
    const precisionMap = new Map([
        ['BTCUSDT', { lotSize: 0.001, precision: 3 }],
        ['ETHUSDT', { lotSize: 0.01, precision: 2 }],
        ['SKATEUSDT', { lotSize: 1, precision: 0 }],
        ['DOGEUSDT', { lotSize: 1, precision: 0 }]
    ]);
    
    const startTime = performance.now();
    
    testCases.forEach(test => {
        const totalValue = test.amount * test.leverage;
        const theoreticalQuantity = totalValue / test.price;
        
        const precision = precisionMap.get(test.symbol);
        if (precision) {
            const roundedQuantity = Math.floor(theoreticalQuantity / precision.lotSize) * precision.lotSize;
            const finalQuantity = precision.precision === 0 ? 
                Math.floor(roundedQuantity) : 
                parseFloat(roundedQuantity.toFixed(precision.precision));
            
            console.log(`📊 ${test.symbol}: ${finalQuantity} (${(finalQuantity * test.price).toFixed(2)} USDT)`);
        }
    });
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`✅ Calculation Time: ${duration.toFixed(4)}ms`);
    console.log(`⚡ Average per calculation: ${(duration / testCases.length).toFixed(4)}ms`);
    
    return { duration, avgPerCalc: duration / testCases.length };
}

// Main test runner
async function runPerformanceTests() {
    console.log('🚀 Starting Performance Tests\n');
    
    try {
        // Test API performance
        const apiResult = await testAPIPerformance();
        
        // Test file performance
        const fileResult = testFilePerformance();
        
        // Test RAM performance
        const ramResult = testRAMPerformance();
        
        // Test calculation performance
        const calcResult = testQuantityCalculation();
        
        // Summary
        console.log('\n📊 PERFORMANCE SUMMARY');
        console.log('='.repeat(50));
        console.log(`🌐 API Call:           ${apiResult.duration.toFixed(2)}ms`);
        console.log(`📁 File Read:          ${fileResult.duration.toFixed(2)}ms`);
        console.log(`⚡ RAM Access:         ${ramResult.avgPerLookup.toFixed(6)}ms per lookup`);
        console.log(`🧮 Calculation:       ${calcResult.avgPerCalc.toFixed(4)}ms per calc`);
        
        console.log('\n🏆 WINNER ANALYSIS:');
        console.log(`🥇 Fastest Access:     RAM (${ramResult.avgPerLookup.toFixed(6)}ms)`);
        console.log(`🥈 Second:             File (${fileResult.duration.toFixed(2)}ms)`);
        console.log(`🥉 Third:              API (${apiResult.duration.toFixed(2)}ms)`);
        
        const speedupVsAPI = apiResult.duration / ramResult.avgPerLookup;
        const speedupVsFile = fileResult.duration / ramResult.avgPerLookup;
        
        console.log(`\n⚡ SPEED IMPROVEMENTS:`);
        console.log(`📈 RAM vs API:         ${speedupVsAPI.toFixed(0)}x faster`);
        console.log(`📈 RAM vs File:        ${speedupVsFile.toFixed(0)}x faster`);
        
        console.log('\n💡 RECOMMENDATION:');
        console.log('✅ Use RAM cache loaded on startup for ultra-fast precision lookups');
        console.log('✅ Load exchange info once when "İşlem Başlat" is clicked');
        console.log('✅ Each quantity calculation will be ~0.0001ms instead of 100-300ms');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

runPerformanceTests();
