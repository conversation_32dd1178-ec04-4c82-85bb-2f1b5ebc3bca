import { useState, useCallback, useRef, useEffect } from 'react';
import { useToast } from "@/hooks/use-toast";
import { Trade } from '@/types/trading';
import { validateTradeParameters } from '@/utils/tradingRules';
import { getSymbolPricePrecision, calculateTradeQuantity } from '@/utils/tradingCalculations';
import { calculateCommissionFee, canOpenTrade } from '@/utils/tradingCalculations';
import {
    BinanceAuthCredentials,
    RealOrderParams,
    BinanceOrderResponse,
    BinancePosition
} from '@/services/binanceService';
import { getWebSocketAPI } from '@/services/binanceWebSocketService';
import positionModeManager, { initializePositionMode } from '@/services/positionModeManager';

export interface RealTradingConfig {
    credentials: BinanceAuthCredentials;
    leverage: number;
    maxOpenPositions: number;
    minVolumeForTrade: number;
    emergencyStopLoss: number;
    maxLeverage: number;
    restrictLowVolumeCoins: boolean;
    stopLossPercent?: number;
    takeProfitPercent?: number;
}

export interface RealTradeParams {
    symbol: string;
    direction: 'long' | 'short';
    amount: number;
    currentPrice: number;
    volume24h: number;
    isTPOrSL?: boolean;
}

const useRealTrading = (config: RealTradingConfig) => {
    const [isPlacingOrder, setIsPlacingOrder] = useState(false);
    const [positions, setPositions] = useState<BinancePosition[]>([]);
    const { toast } = useToast();

    // 🚨 DUPLICATE TRADE PREVENTION - ACTIVE ORDERS TRACKING
    const activeOrdersRef = useRef<Set<string>>(new Set());
    const lastOrderTimeRef = useRef<Record<string, number>>({});

    // WebSocket API instance'ını al (yeniden kullanılabilir)
    const wsAPI = getWebSocketAPI(config.credentials);

    // Position mode'u initialize et
    useEffect(() => {
        const initPositionMode = async () => {
            try {
                if (wsAPI && config.credentials.apiKey) {
                    console.log('🔧 Position mode manager initialize ediliyor...');

                    // Global olarak erişilebilir hale getir
                    (window as any).positionModeManager = positionModeManager;

                    await initializePositionMode(wsAPI);
                    console.log('✅ Position mode manager hazır');

                    // Debug için current mode'u logla
                    console.log('📊 Current position mode:', positionModeManager.getCurrentMode());
                }
            } catch (error) {
                console.error('❌ Position mode manager initialize hatası:', error);
                // Hata durumunda One-way mode'a zorla
                positionModeManager.setMode('ONE_WAY');
                console.log('🔧 Position mode ONE_WAY olarak ayarlandı (fallback)');
            }
        };

        // Biraz gecikme ile initialize et
        setTimeout(initPositionMode, 1000);
    }, [wsAPI, config.credentials.apiKey]);

    // Gerçek Binance pozisyon monitoring sistemi
    useEffect(() => {
        const checkRealPositions = async () => {
            if (!wsAPI) return;

            try {
                // Binance'dan gerçek pozisyonları al
                const realPositions = await wsAPI.getRealPositions();
                const realOpenSymbols = realPositions.map(pos => pos.symbol);

                console.log(`📊 REAL POSITION CHECK: ${realOpenSymbols.length} gerçek açık pozisyon`);

                // Aktif emirleri kontrol et
                const activeSymbols = Array.from(activeOrdersRef.current);

                activeSymbols.forEach(symbol => {
                    // Eğer bu sembol için gerçek açık pozisyon yoksa, aktif emirlerden kaldır
                    if (!realOpenSymbols.includes(symbol)) {
                        console.log(`🧹 REAL POSITION CLOSED: ${symbol} için gerçek pozisyon kapanmış, temizleniyor`);
                        activeOrdersRef.current.delete(symbol);

                        // Last order time'ı da temizle
                        if (lastOrderTimeRef.current[symbol]) {
                            delete lastOrderTimeRef.current[symbol];
                            console.log(`🧹 CLEARED: ${symbol} için tüm referanslar temizlendi`);
                        }
                    }
                });

                // Global olarak gerçek pozisyonları paylaş
                (window as any).realPositions = realPositions;
                (window as any).realOpenPositionsCount = realPositions.length;

            } catch (error) {
                console.error('❌ Gerçek pozisyon kontrolü hatası:', error);
            }
        };

        // Her 30 saniyede bir kontrol et
        const interval = setInterval(checkRealPositions, 30000);

        // İlk kontrolü 5 saniye sonra yap (WebSocket bağlantısı için bekle)
        setTimeout(checkRealPositions, 5000);

        return () => clearInterval(interval);
    }, [wsAPI]);

    // TP/SL tamamlanma event'lerini dinle
    useEffect(() => {
        const handlePositionClosedByTPSL = (event: CustomEvent) => {
            const { symbol, orderType, orderData } = event.detail;
            console.log(`🎯 TP/SL ile pozisyon kapandı:`, { symbol, orderType });

            // Aktif emirlerden tamamen temizle
            console.log(`🧹 CLEANING AFTER TP/SL: ${symbol} için tüm referanslar temizleniyor`);
            activeOrdersRef.current.delete(symbol);

            // Last order time'ı da temizle
            if (lastOrderTimeRef.current[symbol]) {
                delete lastOrderTimeRef.current[symbol];
                console.log(`🧹 CLEARED LAST ORDER TIME: ${symbol} için timestamp temizlendi`);
            }

            console.log(`📝 ACTIVE ORDERS AFTER TP/SL CLEANUP:`, {
                cleanedSymbol: symbol,
                activeOrdersCount: activeOrdersRef.current.size,
                activeOrders: Array.from(activeOrdersRef.current),
                lastOrderTimesCount: Object.keys(lastOrderTimeRef.current).length,
                timestamp: new Date().toISOString()
            });

            // Toast bildirim
            toast({
                title: orderType === 'TAKE_PROFIT_MARKET' ? "Take Profit Gerçekleşti" : "Stop Loss Gerçekleşti",
                description: `${symbol} pozisyonu ${orderType === 'TAKE_PROFIT_MARKET' ? 'kar alarak' : 'zarar keserek'} kapandı. Yeni işlemler açılabilir.`,
                variant: orderType === 'TAKE_PROFIT_MARKET' ? "default" : "destructive",
            });
        };

        const handleTradeCompleted = (event: CustomEvent) => {
            const tradeRecord = event.detail;
            console.log(`📝 İşlem tamamlandı:`, tradeRecord);

            // İşlem geçmişine ekle (bu event'i diğer componentler de dinleyebilir)
            // Burada ek işlemler yapılabilir
        };

        // Event listener'ları ekle
        window.addEventListener('position-closed-by-tpsl', handlePositionClosedByTPSL as EventListener);
        window.addEventListener('trade-completed', handleTradeCompleted as EventListener);

        // Cleanup
        return () => {
            window.removeEventListener('position-closed-by-tpsl', handlePositionClosedByTPSL as EventListener);
            window.removeEventListener('trade-completed', handleTradeCompleted as EventListener);
        };
    }, [toast]);

    // 🔒 DUPLICATE TRADE PREVENTION FUNCTION - İYİLEŞTİRİLMİŞ
    const isDuplicateTradeAttempt = (symbol: string, isTPOrSL: boolean = false): boolean => {
        // TP/SL işlemleri için duplicate kontrolünü atla
        if (isTPOrSL) {
            console.log(`🎯 TP/SL EMRİ: ${symbol} - Duplicate kontrolü atlanıyor`);
            return false;
        }

        const now = Date.now();
        const lastOrderTime = lastOrderTimeRef.current[symbol] || 0;
        const timeDiff = now - lastOrderTime;
        const isActiveInRef = activeOrdersRef.current.has(symbol);

        console.log(`🔍 DUPLICATE CHECK for ${symbol}:`, {
            isActiveInRef,
            lastOrderTime: lastOrderTime ? new Date(lastOrderTime).toISOString() : 'never',
            timeDiff: `${timeDiff}ms`,
            timeDiffMinutes: `${Math.round(timeDiff/60000)}min`,
            activeOrdersCount: activeOrdersRef.current.size,
            activeOrders: Array.from(activeOrdersRef.current),
            minIntervalMs: '300000ms (5min)'
        });

        // Aktif emir kontrol et - Sadece aynı sembol için engelle
        if (isActiveInRef) {
            console.log(`🚫 DUPLICATE PREVENTION (ACTIVE): ${symbol} için işlem süreci devam ediyor`);
            return true;
        }

        // Aynı symbol için 5 dakika içinde ikinci emir engelle
        if (timeDiff < 300000) { // 5 dakika = 300000ms
            console.log(`🚫 DUPLICATE PREVENTION (TIME): ${symbol} için ${Math.round(timeDiff/1000)} saniye önce emir verildi, 5 dakika beklenmeli`);
            return true;
        }

        console.log(`✅ DUPLICATE CHECK PASSED: ${symbol} için işlem yapılabilir`);
        return false;
    };

    // Aktif emirleri belirli aralıklarla temizleme - 10 dakika sonra otomatik temizle
    useEffect(() => {
        const cleanupInterval = setInterval(() => {
            const now = Date.now();
            // 10 dakikadan eski tüm emirleri temizle (daha uzun süre)
            const oldOrders = Object.entries(lastOrderTimeRef.current)
                .filter(([_, timestamp]) => now - timestamp > 10 * 60 * 1000)
                .map(([symbol]) => symbol);

            if (oldOrders.length > 0) {
                console.log(`🧹 Eski emirler temizleniyor (10 dakikadan eski):`, oldOrders);

                for (const symbol of oldOrders) {
                    activeOrdersRef.current.delete(symbol);
                    delete lastOrderTimeRef.current[symbol];
                }
            }
        }, 60000); // 1 dakikada bir kontrol et (daha sık kontrol)

        return () => clearInterval(cleanupInterval);
    }, []);

    const placeRealTrade = useCallback(async (params: RealTradeParams) => {
        console.log('🚀 GERÇEK İŞLEM AÇILIYOR:', params);

        // 🔒 DUPLICATE TRADE PREVENTION - TP/SL emirleri için ayrı kontrol
        if (isDuplicateTradeAttempt(params.symbol, params.isTPOrSL)) {
            toast({
                title: "Duplicate İşlem Engellendi",
                description: `${params.symbol} için zaten aktif bir işlem var veya çok yakın zamanda işlem yapıldı.`,
                variant: "destructive",
            });
            return {
                success: false,
                error: "Duplicate trade attempt blocked",
                data: null
            };
        }

        // TP/SL emirleri için özel log
        if (params.isTPOrSL) {
            console.log(`🎯 TP/SL EMRİ: ${params.symbol} - Duplicate kontrolü atlandı`);
        }

        // Maximum pozisyon sayısı kontrolü - İYİLEŞTİRİLMİŞ
        const currentSymbolPositions = Array.from(activeOrdersRef.current).filter(s => s === params.symbol).length;
        const totalOpenPositions = Array.from(activeOrdersRef.current).length;

        console.log(`📊 POSITION LIMITS CHECK for ${params.symbol}:`, {
            currentSymbolPositions,
            totalOpenPositions,
            maxOpenPositions: config.maxOpenPositions,
            isTPOrSL: params.isTPOrSL,
            activeOrdersArray: Array.from(activeOrdersRef.current)
        });

        // Aynı sembol için sadece 1 işlem kontrolü
        if (!params.isTPOrSL && currentSymbolPositions >= 1) {
            console.log(`🚫 SYMBOL LIMIT: ${params.symbol} için zaten ${currentSymbolPositions} işlem var`);
            toast({
                title: "Çoklu İşlem Engellendi",
                description: `${params.symbol} için zaten açık bir işlem var. Aynı coin için 2 işlem aynı anda açık olamaz.`,
                variant: "destructive",
            });
            return {
                success: false,
                error: `Duplicate position blocked for ${params.symbol} - only one position per symbol allowed`,
                data: null
            };
        }

        // Toplam maksimum pozisyon sayısı kontrolü
        if (!params.isTPOrSL && totalOpenPositions >= config.maxOpenPositions) {
            console.log(`🚫 TOTAL LIMIT: Toplam ${totalOpenPositions} işlem var, maksimum ${config.maxOpenPositions}`);
            toast({
                title: "Maximum İşlem Limiti",
                description: `Maksimum açık işlem sayısına ulaştınız (${config.maxOpenPositions})`,
                variant: "destructive",
            });
            return {
                success: false,
                error: `Maximum total open positions limit reached (${config.maxOpenPositions})`,
                data: null
            };
        }

        console.log(`✅ POSITION LIMITS PASSED: ${params.symbol} için işlem yapılabilir`);


        setIsPlacingOrder(true);

        try {
            // İşlemi aktif olarak işaretle - Sadece normal işlemler için
            if (!params.isTPOrSL) {
                console.log(`🔄 MARKING ACTIVE: ${params.symbol} işlemi aktif olarak işaretleniyor`);
                activeOrdersRef.current.add(params.symbol);
                lastOrderTimeRef.current[params.symbol] = Date.now();
                console.log(`📝 ACTIVE ORDERS UPDATED:`, {
                    symbol: params.symbol,
                    activeOrdersCount: activeOrdersRef.current.size,
                    activeOrders: Array.from(activeOrdersRef.current),
                    timestamp: new Date().toISOString()
                });
            } else {
                console.log(`🎯 TP/SL işlemi - Aktif işlem listesine eklenmedi: ${params.symbol}`);
            }

            // Trade parametrelerini doğrula
            const validation = validateTradeParameters(params.symbol, params.amount, config.leverage, params.currentPrice);
            if (!validation.isValid) {
                throw new Error(validation.reason || 'Geçersiz işlem parametreleri');
            }

            // 24 saatlik hacim kontrolü - TP/SL için daha esnek olabilir
            const volume24h = params.volume24h;
            const volumeInMillions = volume24h / 1000000;

            // Hacim kontrolü (TP/SL için atla veya daha esnek kural uygula)
            if (!params.isTPOrSL && volumeInMillions < config.minVolumeForTrade) {
                throw new Error(`İşlem hacmi (${volumeInMillions.toFixed(2)}M$) minimum değerin altında (${config.minVolumeForTrade}M$)`);
            }

            // Pozisyon miktarını hesapla (ultra fast precision ile)
            const quantity = calculateTradeQuantity(params.symbol, params.currentPrice, params.amount, config.leverage);

            // Price precision'ı al
            const { pricePrecision } = getSymbolPricePrecision(params.symbol);
            const formattedPrice = parseFloat(params.currentPrice.toFixed(pricePrecision));

            console.log('📊 İşlem hesaplamaları:', {
                symbol: params.symbol,
                direction: params.direction,
                amount: params.amount,
                leverage: config.leverage,
                currentPrice: params.currentPrice,
                formattedPrice,
                quantity,
                pricePrecision,
                isTPOrSL: params.isTPOrSL || false
            });

            // WebSocket bağlantısı kontrolü
            if (!wsAPI.isConnected || !wsAPI.isAuthenticated) {
                console.log('🔄 WebSocket bağlantısı kontrol ediliyor...');

                try {
                    // WebSocket connection timeout ile dene
                    const connectionPromise = wsAPI.connect();
                    const timeoutPromise = new Promise((_, reject) =>
                        setTimeout(() => reject(new Error('WebSocket connection timeout')), 10000)
                    );

                    await Promise.race([connectionPromise, timeoutPromise]);

                    // Yeniden kontrol
                    if (!wsAPI.isConnected || !wsAPI.isAuthenticated) {
                        console.warn('⚠️ WebSocket kimlik doğrulama başarısız, REST API kullanılacak');
                        // Doğrudan REST API kullanarak işleme devam et - hata fırlatma
                    }

                    console.log('✅ WebSocket bağlantısı başarılı');
                } catch (wsError) {
                    console.warn('⚠️ WebSocket bağlantısı başarısız, REST API kullanılacak:', wsError);
                    // Doğrudan REST API kullanarak işleme devam et - hata fırlatma
                }
            }

            // Kaldıraç ayarla (WebSocket API)
            try {
                await wsAPI.setLeverage(params.symbol, config.leverage);
                console.log(`✅ Kaldıraç ayarlandı: ${params.symbol} = ${config.leverage}x`);
            } catch (leverageError) {
                console.warn('⚠️ Kaldıraç ayarlanamadı (zaten ayarlı olabilir):', leverageError);
                // Kaldıraç hatası işlemi engellemez, devam et
            }

            // WebSocket API ile market emri ver
            let orderResult: BinanceOrderResponse;

            if (params.direction === 'short') {
                // SHORT pozisyon aç
                orderResult = await wsAPI.openShortPosition(params.symbol, quantity.toString(), config.leverage);
                console.log(`✅ SHORT pozisyon açıldı (WebSocket) - ${params.isTPOrSL ? 'TP/SL' : 'Normal'}:`, orderResult);
            } else {
                // LONG pozisyon aç
                orderResult = await wsAPI.openLongPosition(params.symbol, quantity.toString(), config.leverage);
                console.log(`✅ LONG pozisyon açıldı (WebSocket) - ${params.isTPOrSL ? 'TP/SL' : 'Normal'}:`, orderResult);
            }

            // İşlem başarılı oldu - Aktif listeden temizleme işlemi burada yapılsın
            if (!params.isTPOrSL) {
                console.log(`✅ TRADE SUCCESS: ${params.symbol} işlemi başarılı, aktif listeden kaldırılıyor`);
                activeOrdersRef.current.delete(params.symbol);
                console.log(`📝 ACTIVE ORDERS AFTER SUCCESS:`, {
                    removedSymbol: params.symbol,
                    activeOrdersCount: activeOrdersRef.current.size,
                    activeOrders: Array.from(activeOrdersRef.current),
                    timestamp: new Date().toISOString()
                });
            }

            // Başarılı sonuç döndür
            console.log(`✅ Trade completed successfully for ${params.symbol} - ${params.isTPOrSL ? 'TP/SL' : 'Normal'}`);

            // Normal işlem başarılı ise ve TP/SL değilse, TP/SL emirlerini otomatik ekle
            if (!params.isTPOrSL && config.takeProfitPercent && config.stopLossPercent) {
                try {
                    console.log(`🎯 ${params.symbol} için otomatik TP/SL emirleri ekleniyor...`);

                    // Güvenlik kontrolü: TP/SL yüzdeleri sinyal eşik yüzdesinden büyük olmamalı
                    // Eşik yüzdesi config'den alınır (örn: 0.9% sinyal eşiği)
                    const signalThresholdPercent = 0.9; // Varsayılan değer, gerçek değer config'den gelecek

                    if (config.takeProfitPercent > signalThresholdPercent) {
                        console.warn(`⚠️ GÜVENLİK UYARISI: Take Profit yüzdesi (${config.takeProfitPercent}%) sinyal eşik yüzdesinden (${signalThresholdPercent}%) büyük! Yüksek kaybetme riski!`);
                        toast({
                            title: "Yüksek Risk Uyarısı",
                            description: `Take Profit yüzdesi (${config.takeProfitPercent}%) çok yüksek! Sinyal eşiği: ${signalThresholdPercent}%`,
                            variant: "destructive",
                        });
                    }

                    if (config.stopLossPercent > signalThresholdPercent) {
                        console.warn(`⚠️ GÜVENLİK UYARISI: Stop Loss yüzdesi (${config.stopLossPercent}%) sinyal eşik yüzdesinden (${signalThresholdPercent}%) büyük! Yüksek kaybetme riski!`);
                        toast({
                            title: "Yüksek Risk Uyarısı",
                            description: `Stop Loss yüzdesi (${config.stopLossPercent}%) çok yüksek! Sinyal eşiği: ${signalThresholdPercent}%`,
                            variant: "destructive",
                        });
                    }

                    // Price precision'ı al
                    const { pricePrecision } = getSymbolPricePrecision(params.symbol);

                    // Take Profit emri - Kaldıraç fiyat hedefini etkilemez, sadece pozisyon büyüklüğünü etkiler
                    // Örnek: SHORT pozisyon, entry_price=50000, TP=%0.5 → TP_price=50000*(1-0.005)=49750
                    const tpPriceRaw = params.direction === 'long'
                        ? params.currentPrice * (1 + config.takeProfitPercent / 100)  // LONG: fiyat yükselsin
                        : params.currentPrice * (1 - config.takeProfitPercent / 100); // SHORT: fiyat düşsün
                    const tpPrice = parseFloat(tpPriceRaw.toFixed(pricePrecision));

                    // Stop Loss emri - Kaldıraç fiyat hedefini etkilemez
                    // Örnek: SHORT pozisyon, entry_price=50000, SL=%0.5 → SL_price=50000*(1+0.005)=50250
                    const slPriceRaw = params.direction === 'long'
                        ? params.currentPrice * (1 - config.stopLossPercent / 100)  // LONG: fiyat düşerse zarar kes
                        : params.currentPrice * (1 + config.stopLossPercent / 100); // SHORT: fiyat yükselirse zarar kes
                    const slPrice = parseFloat(slPriceRaw.toFixed(pricePrecision));

                    console.log(`📊 TP/SL fiyatları hesaplandı:`, {
                        symbol: params.symbol,
                        direction: params.direction,
                        currentPrice: params.currentPrice,
                        pricePrecision,
                        tpPrice,
                        slPrice
                    });

                    // TP emrini REST API ile gönder
                    const tpParams: RealTradeParams = {
                        ...params,
                        isTPOrSL: true
                    };

                    // SL emrini REST API ile gönder
                    const slParams: RealTradeParams = {
                        ...params,
                        isTPOrSL: true
                    };

                    // Asenkron olarak gönder
                    setTimeout(async () => {
                        try {
                            // TP ve SL emirlerini burada özel fonksiyonlarla gönder
                            console.log(`🎯 ${params.symbol} için TP/SL emirleri gönderiliyor...`);

                            // TP ve SL için işlem yönünü belirle (LONG için SELL, SHORT için BUY)
                            const closeSide = params.direction === 'long' ? 'SELL' : 'BUY';

                            // Quantity hesapla (ultra fast precision ile)
                            const tpslQuantity = calculateTradeQuantity(params.symbol, params.currentPrice, params.amount, config.leverage);

                            // Futures'da TP/SL emirlerini ayrı ayrı gönder (GTE_GTC ile)
                            // Take Profit emrini gönder
                            const tpResult = await wsAPI.placeTakeProfitOrder(
                                params.symbol,
                                closeSide,
                                tpslQuantity.toString(),
                                tpPrice.toString()
                            );

                            console.log(`✅ Take Profit emri başarıyla gönderildi (GTE_GTC):`, tpResult);

                            // Stop Loss emrini gönder
                            const slResult = await wsAPI.placeStopLossOrder(
                                params.symbol,
                                closeSide,
                                tpslQuantity.toString(),
                                slPrice.toString()
                            );

                            console.log(`✅ Stop Loss emri başarıyla gönderildi (GTE_GTC):`, slResult);

                            // Ekranda bildir
                            toast({
                                title: "TP/SL Emirleri Eklendi",
                                description: `${params.symbol} için otomatik TP/SL emirleri başarıyla eklendi.`,
                                variant: "default",
                            });
                        } catch (error) {
                            console.error(`❌ TP/SL emirleri gönderilirken hata: ${error instanceof Error ? error.message : error}`);

                            toast({
                                title: "TP/SL Emirleri Eklenemedi",
                                description: `Hata: ${error instanceof Error ? error.message : JSON.stringify(error)}`,
                                variant: "destructive",
                            });
                        }
                    }, 500); // Asıl işlemden 500ms sonra gönder
                } catch (tpslError) {
                    console.error(`❌ TP/SL emirleri oluşturma hatası: ${tpslError}`);
                }
            }

            return {
                success: true,
                orderId: orderResult.orderId,
                executedQty: orderResult.executedQty,
                executedPrice: orderResult.avgPrice || orderResult.price,
                data: orderResult
            };

        } catch (error) {
            console.error('❌ GERÇEK İŞLEM HATASI:', error);

            // Başarısız işlem için hem aktif listeyi hem timestamp'i temizle - Sadece normal işlemler için
            if (!params.isTPOrSL) {
                console.log(`❌ TRADE FAILED: ${params.symbol} işlemi başarısız, aktif listeden kaldırılıyor`);
                activeOrdersRef.current.delete(params.symbol);

                if (lastOrderTimeRef.current[params.symbol]) {
                    console.log(`🧹 CLEARING FAILED TIMESTAMP: ${params.symbol} için timestamp temizleniyor`);
                    delete lastOrderTimeRef.current[params.symbol];
                }

                console.log(`📝 ACTIVE ORDERS AFTER FAILURE:`, {
                    failedSymbol: params.symbol,
                    activeOrdersCount: activeOrdersRef.current.size,
                    activeOrders: Array.from(activeOrdersRef.current),
                    timestamp: new Date().toISOString()
                });
            }

            toast({
                title: "İşlem Hatası",
                description: `Hata: ${error instanceof Error ? error.message : JSON.stringify(error)}`,
                variant: "destructive",
            });

            return {
                success: false,
                error: error instanceof Error ? error.message : JSON.stringify(error),
                data: null
            };
        } finally {
            setIsPlacingOrder(false);
        }
    }, [wsAPI, toast, config.leverage, config.minVolumeForTrade, config.takeProfitPercent, config.stopLossPercent]);

    const closeRealTrade = useCallback(async (trade: Trade) => {
        console.log('🔴 GERÇEK İŞLEM KAPATILIYOR:', trade);

        setIsPlacingOrder(true);

        try {
            // WebSocket bağlantısı kontrolü
            if (!wsAPI.isConnected || !wsAPI.isAuthenticated) {
                // Bağlantıyı yeniden dene
                await wsAPI.connect();

                if (!wsAPI.isConnected || !wsAPI.isAuthenticated) {
                    throw new Error('WebSocket bağlantısı kurulamadı');
                }
            }

            // Pozisyon yönünü belirle
            const positionSide = trade.direction === 'long' ? 'LONG' : 'SHORT';

            // WebSocket ile pozisyonu kapat
            const closeResult = await wsAPI.closePosition(trade.symbol, positionSide);
            console.log('✅ WebSocket ile pozisyon kapatıldı:', closeResult);

            return {
                success: true,
                orderId: closeResult.orderId,
                data: closeResult
            };

        } catch (error) {
            console.error('❌ POZISYON KAPATMA HATASI:', error);

            toast({
                title: "Pozisyon Kapatma Hatası",
                description: `Hata: ${error instanceof Error ? error.message : JSON.stringify(error)}`,
                variant: "destructive",
            });

            return {
                success: false,
                error: error instanceof Error ? error.message : JSON.stringify(error)
            };
        } finally {
            setIsPlacingOrder(false);
        }
    }, [wsAPI, toast]);

    const refreshPositions = useCallback(async () => {
        try {
            // WebSocket API ile pozisyonları al
            if (wsAPI.isConnected && wsAPI.isAuthenticated) {
                const currentPositions = await wsAPI.getPositions();
                setPositions(currentPositions);
                console.log('✅ Pozisyonlar güncellendi (WebSocket):', currentPositions.length, 'pozisyon');
            }
        } catch (error) {
            console.error('❌ Pozisyonlar güncellenirken hata:', error);
            toast({
                title: "Pozisyon Güncelleme Hatası",
                description: `Hata: ${error instanceof Error ? error.message : JSON.stringify(error)}`,
                variant: "destructive",
            });
        }
    }, [wsAPI, toast]);

    const validateRealTrade = useCallback((params: RealTradeParams) => {
        const validation = validateTradeParameters(params.symbol, params.amount, config.leverage, params.currentPrice);
        return validation;
    }, [config.leverage]);

    // 🧹 Cleanup function for active orders - İYİLEŞTİRİLMİŞ
    const clearActiveOrders = useCallback(() => {
        console.log('🧹 MANUAL CLEANUP: Tüm aktif emirler temizleniyor:', {
            activeOrdersCount: activeOrdersRef.current.size,
            activeOrders: Array.from(activeOrdersRef.current),
            lastOrderTimesCount: Object.keys(lastOrderTimeRef.current).length,
            lastOrderTimes: Object.keys(lastOrderTimeRef.current)
        });
        activeOrdersRef.current.clear();
        lastOrderTimeRef.current = {};
        console.log('✅ MANUAL CLEANUP COMPLETED: Tüm aktif emirler temizlendi');
    }, []);

    // 🔧 Emergency cleanup function - Global olarak erişilebilir
    useEffect(() => {
        // Global cleanup fonksiyonunu window'a ekle
        (window as any).clearActiveOrders = clearActiveOrders;
        (window as any).debugActiveOrders = () => {
            console.log('🔍 DEBUG ACTIVE ORDERS:', {
                activeOrdersCount: activeOrdersRef.current.size,
                activeOrders: Array.from(activeOrdersRef.current),
                lastOrderTimesCount: Object.keys(lastOrderTimeRef.current).length,
                lastOrderTimes: lastOrderTimeRef.current
            });
        };

        // Position mode debug fonksiyonu
        (window as any).debugPositionMode = () => {
            console.log('🔍 DEBUG POSITION MODE:', {
                isReady: positionModeManager.isReady(),
                currentMode: positionModeManager.getCurrentMode(),
                positionSideForShort: positionModeManager.getPositionSide('SHORT'),
                positionSideForLong: positionModeManager.getPositionSide('LONG')
            });
        };

        // WebSocket debug fonksiyonu
        (window as any).debugWebSocket = () => {
            console.log('🔍 DEBUG WEBSOCKET:', {
                wsAPI: !!wsAPI,
                credentials: !!config.credentials.apiKey,
                isConnected: wsAPI ? 'Unknown' : 'Not initialized'
            });
        };

        // Event listener test fonksiyonu
        (window as any).testTPSLEvent = (symbol = 'TESTUSDT') => {
            console.log('🧪 TEST TP/SL EVENT:', symbol);
            window.dispatchEvent(new CustomEvent('position-closed-by-tpsl', {
                detail: {
                    symbol,
                    orderType: 'TAKE_PROFIT_MARKET',
                    orderId: 'test123',
                    orderData: { test: true }
                }
            }));
        };

        // Emergency reset fonksiyonu
        (window as any).emergencyReset = () => {
            console.log('🚨 EMERGENCY RESET: Tüm aktif emirler ve zamanlar temizleniyor');
            activeOrdersRef.current.clear();
            lastOrderTimeRef.current = {};
            console.log('✅ EMERGENCY RESET COMPLETED');
        };

        // Gerçek pozisyon debug fonksiyonu
        (window as any).debugRealPositions = async () => {
            if (!wsAPI) {
                console.log('❌ WebSocket API not available');
                return;
            }

            try {
                const realPositions = await wsAPI.getRealPositions();
                console.log('🔍 DEBUG REAL POSITIONS:', {
                    count: realPositions.length,
                    positions: realPositions,
                    symbols: realPositions.map(p => p.symbol),
                    globalCount: (window as any).realOpenPositionsCount,
                    globalSymbols: (window as any).openPositionSymbols
                });
            } catch (error) {
                console.error('❌ Real positions debug error:', error);
            }
        };

        // Position status debug fonksiyonu
        (window as any).debugPositionStatus = () => {
            console.log('🔍 DEBUG POSITION STATUS:', {
                realOpenPositionsCount: (window as any).realOpenPositionsCount,
                openPositionSymbols: (window as any).openPositionSymbols,
                realPositions: (window as any).realPositions,
                timestamp: new Date().toISOString()
            });
        };

        return () => {
            delete (window as any).clearActiveOrders;
            delete (window as any).debugActiveOrders;
            delete (window as any).debugPositionMode;
        };
    }, [clearActiveOrders]);

    return {
        placeRealTrade,
        closeRealTrade,
        refreshPositions,
        validateRealTrade,
        isPlacingOrder,
        positions,
        clearActiveOrders
    };
};

export default useRealTrading;
