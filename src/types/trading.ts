export interface Trade {
  id: string;
  symbol: string;
  entryPrice: number;
  quantity: number;
  direction: 'long' | 'short';
  openTime: Date;
  status: 'open' | 'closed';
  leverage?: number;
  investmentAmount?: number;
  profitLoss?: number;
  exitPrice?: number;
  exitTime?: Date;
  actualProfitAmount?: number; // Actual profit/loss amount
  commissionFee?: number; // Commission fee for the trade
  fundingFee?: number; // Funding fee for the trade
  totalFees?: number; // Total fees (commission + funding)
  entryCommission?: number; // Commission at entry
  exitCommission?: number; // Commission at exit
  isMaker?: boolean; // Whether the trade is a maker (true) or taker (false)
  // Real trading fields
  binanceOrderId?: number; // Binance order ID for real trades
  clientOrderId?: string; // Client order ID for real trades
}

export interface TradeStats {
  totalTrades: number;
  profitableTrades: number;
  lossTrades: number;
  dailyChangePercent: number;
  totalProfitAmount?: number; // Total profit/loss amount
  totalFees?: number; // Total fees paid
  totalNetProfit?: number; // Total profit after fees
  openTrades?: number; // Number of open trades
  closedTrades?: number; // Number of closed trades
  netProfitPercentage?: number; // Net profit percentage after fees
}

export interface TradingPanelProps {
  isAutoTradingEnabled: boolean;
  trades: Trade[];
  tradingStatus: 'inactive' | 'connecting' | 'active' | 'error';
  tradingDirection: 'long' | 'short';
  setTradingDirection: (direction: 'long' | 'short') => void;
  tradeUnit: number;
  setTradeUnit: (unit: number) => void;
  profitTarget: number;
  setProfitTarget: (target: number) => void;
  stopLoss: number;
  setStopLoss: (loss: number) => void;
  selectedTradingSymbol: string;
  setSelectedTradingSymbol: (symbol: string) => void;
  availableSymbols: string[];
  leverage: number;
  setLeverage: (leverage: number) => void;
  stopNewTrades: boolean;
  setStopNewTrades: (stop: boolean) => void;
}

export interface BinanceOrderParams {
  symbol: string;
  side: 'BUY' | 'SELL';
  type: string;
  quantity: string;
  price?: string;
  timeInForce?: string;
  stopPrice?: string;
  recvWindow?: number;
}

// New interfaces for account information
export interface AccountBalance {
  asset: string;
  free?: string; // Added from TradeMonitor
  locked?: string; // Added from TradeMonitor
  walletBalance: string;
  crossWalletBalance: string;
  crossUnPnl?: string; // Added from TradeMonitor
  availableBalance?: string; // Added from TradeMonitor
  marginBalance?: string; // Added from TradeMonitor
  positionInitialMargin?: string; // Added from TradeMonitor
  totalPositionInitialMargin?: string; // Added from TradeMonitor
  unrealizedProfit?: string; // Added from TradeMonitor
  balanceChange?: string;
}

export interface AccountPosition {
  symbol: string;
  positionAmount: string;
  entryPrice: string;
  unrealizedProfit: string;
  marginType: 'isolated' | 'cross';
  isolatedWallet: string;
  positionSide: string;
  leverage: string;
}

export interface AccountInfo {
  balances: AccountBalance[];
  positions: AccountPosition[];
  availableBalance: string;
  totalWalletBalance: string;
  totalCrossMarginBalance: string;
  totalUnrealizedProfit: string;
  maxWithdrawAmount: string;
  fundingInfo?: FundingRateInfo[];
}

// New interface for funding rate information
export interface FundingRateInfo {
  symbol: string;
  fundingRate: number;
  nextFundingTime: Date;
  fundingInterval: number; // in milliseconds
}

// Interface for trading restrictions
export interface TradingRestrictions {
  restrictNewCoins: boolean;
  maxTotalLossPercent: number;
  avoidNegativeFundingForShort: boolean;
  fundingTimeThresholdMinutes: number;
}

// Interface for symbol information
export interface SymbolInfo {
  symbol: string;
  pricePrecision: number;
  quantityPrecision: number;
  minQuantity: number;
  tickSize: number; // Minimal price movement
}

// Update the CloseTradeReason type to include emergency_stop_loss
export type CloseTradeReason = 'stop_loss' | 'take_profit' | 'emergency_stop_loss';
