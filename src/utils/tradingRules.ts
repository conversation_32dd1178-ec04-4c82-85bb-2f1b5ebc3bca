import tradingRulesData from '../../trading_rules.json';

// Trading kuralları interface tanımları
export interface TradingRule {
  symbol: string;
  contract_size: string;
  price: string;
  tick_size: string;
  fee: string;
  order_limit: string;
  leverage: string;
  maintenance_margin_rate: string;
  liquidation_fee_rate: string;
  min_order_value: string;
  initial_margin_rate: string;
}

// Symbol'den coin adını çıkar (BTCUSDT -> BTC)
export function extractCoinFromSymbol(symbol: string): string {
  return symbol.replace('USDT', '').replace('BUSD', '');
}

// Belirli bir coin için trading kurallarını getir
export function getTradingRulesForSymbol(symbol: string): TradingRule | null {
  console.log('🔍 Trading kuralları aranıyor:', symbol);

  try {
    // trading_rules.json artık Binance exchange info formatında
    const exchangeInfo = tradingRulesData as any;

    if (!exchangeInfo.symbols || !Array.isArray(exchangeInfo.symbols)) {
      console.warn('⚠️ Trading rules dosyası geçersiz format');
      return null;
    }

    // Symbol'ü doğrudan ara
    const symbolInfo = exchangeInfo.symbols.find((s: any) => s.symbol === symbol);

    if (symbolInfo) {
      // Binance formatından TradingRule formatına dönüştür
      const rule: TradingRule = {
        symbol: 'Perpetual',
        contract_size: `${symbolInfo.filters?.find((f: any) => f.filterType === 'LOT_SIZE')?.minQty || '0.001'} ${symbolInfo.baseAsset}`,
        price: `${symbolInfo.filters?.find((f: any) => f.filterType === 'PRICE_FILTER')?.minPrice || '0.01'} /${symbolInfo.filters?.find((f: any) => f.filterType === 'PRICE_FILTER')?.tickSize || '0.01'} USDT`,
        tick_size: symbolInfo.filters?.find((f: any) => f.filterType === 'PRICE_FILTER')?.tickSize || '0.01',
        fee: '10% / 10%', // Default
        order_limit: `${symbolInfo.filters?.find((f: any) => f.filterType === 'MARKET_LOT_SIZE')?.maxQty || '1000000'} /${symbolInfo.filters?.find((f: any) => f.filterType === 'LOT_SIZE')?.maxQty || '1000000'} ${symbolInfo.baseAsset}`,
        leverage: '125', // Default max leverage
        maintenance_margin_rate: symbolInfo.maintMarginPercent || '2.5000',
        liquidation_fee_rate: symbolInfo.liquidationFee || '0.015000',
        min_order_value: `${symbolInfo.filters?.find((f: any) => f.filterType === 'MIN_NOTIONAL')?.notional || '5'} USDT`,
        initial_margin_rate: symbolInfo.requiredMarginPercent || '5.0000'
      };

      console.log('✅ Trading kuralları bulundu:', rule);
      return rule;
    } else {
      console.warn('⚠️ Trading kuralları bulunamadı:', symbol);
      return null;
    }
  } catch (error) {
    console.error('❌ Trading kuralları okuma hatası:', error);
    return null;
  }
}

// Minimum işlem miktarını getir
export function getMinOrderQuantity(symbol: string): number {
  const rules = getTradingRulesForSymbol(symbol);
  if (!rules) return 0.001; // Default değer

  try {
    // Contract size'dan minimum miktarı çıkar (örn: "0.001 BTC" -> 0.001)
    const contractSizeStr = rules.contract_size;
    const matches = contractSizeStr.match(/(\d+\.?\d*)/);
    return matches ? parseFloat(matches[1]) : 0.001;
  } catch (error) {
    console.error('❌ Minimum miktar hesaplama hatası:', error);
    return 0.001;
  }
}

// Maksimum kaldıraç değerini getir
export function getMaxLeverage(symbol: string): number {
  const rules = getTradingRulesForSymbol(symbol);
  if (!rules) return 20; // Default değer

  try {
    return parseInt(rules.leverage) || 20;
  } catch (error) {
    console.error('❌ Maksimum kaldıraç hesaplama hatası:', error);
    return 20;
  }
}

// Fiyat precision'unu getir
export function getPricePrecision(symbol: string): number {
  const rules = getTradingRulesForSymbol(symbol);
  if (!rules) return 2; // Default değer

  try {
    const tickSizeStr = rules.tick_size;
    const decimalPlaces = tickSizeStr.includes('.') ?
      tickSizeStr.split('.')[1].length : 0;
    return decimalPlaces;
  } catch (error) {
    console.error('❌ Fiyat precision hesaplama hatası:', error);
    return 2;
  }
}

// Miktar precision'unu getir - İYİLEŞTİRİLMİŞ
export function getQuantityPrecision(symbol: string): number {
  const rules = getTradingRulesForSymbol(symbol);

  // Özel durumlar - trading rules'a göre
  const specialCases: Record<string, number> = {
    'CAKEUSDT': 0,    // 1 CAKE = integer only
    'BNBUSDT': 2,     // 0.01 BNB precision
    'ADAUSDT': 0,     // 1 ADA = integer only
    'DOGEUSDT': 0,    // 1 DOGE = integer only
    'XRPUSDT': 0,     // 1 XRP = integer only
    'MATICUSDT': 0,   // 1 MATIC = integer only
    'SOLUSDT': 2,     // 0.01 SOL precision
    'AVAXUSDT': 2,    // 0.01 AVAX precision
    'DOTUSDT': 2,     // 0.01 DOT precision
    'LINKUSDT': 2,    // 0.01 LINK precision
    'UNIUSDT': 2,     // 0.01 UNI precision
    'LTCUSDT': 3,     // 0.001 LTC precision
    'BCHUSDT': 3,     // 0.001 BCH precision
    'ETCUSDT': 2,     // 0.01 ETC precision
    'TRXUSDT': 0,     // 1 TRX = integer only
    'XLMUSDT': 0,     // 1 XLM = integer only
  };

  if (specialCases[symbol] !== undefined) {
    console.log(`🎯 PRECISION: ${symbol} için özel precision: ${specialCases[symbol]}`);
    return specialCases[symbol];
  }

  if (!rules) {
    console.log(`⚠️ PRECISION: ${symbol} için trading rules bulunamadı, default: 3`);
    return 3; // Default değer
  }

  try {
    const contractSizeStr = rules.contract_size;
    const matches = contractSizeStr.match(/(\d+\.?\d*)/);
    if (matches) {
      const value = matches[1];
      const decimalPlaces = value.includes('.') ?
        value.split('.')[1].length : 0;
      console.log(`📊 PRECISION: ${symbol} contract_size: ${contractSizeStr}, precision: ${decimalPlaces}`);
      return decimalPlaces;
    }
    console.log(`⚠️ PRECISION: ${symbol} için contract_size parse edilemedi, default: 3`);
    return 3;
  } catch (error) {
    console.error('❌ Miktar precision hesaplama hatası:', error);
    return 3;
  }
}

// Minimum notional değeri getir
export function getMinNotional(symbol: string): number {
  const rules = getTradingRulesForSymbol(symbol);
  if (!rules) return 5; // Default değer

  try {
    const minOrderValueStr = rules.min_order_value;
    const matches = minOrderValueStr.match(/(\d+\.?\d*)/);
    return matches ? parseFloat(matches[1]) : 5;
  } catch (error) {
    console.error('❌ Minimum notional hesaplama hatası:', error);
    return 5;
  }
}

// Trading kurallarını validate et
export function validateTradeAmount(symbol: string, quantity: number, price: number): {
  isValid: boolean;
  reason?: string;
  adjustedQuantity?: number;
} {
  console.log('🔍 Trade validasyonu:', { symbol, quantity, price });

  try {
    const rules = getTradingRulesForSymbol(symbol);
    if (!rules) {
      return { isValid: true }; // Kural yoksa geç
    }

    // Minimum miktar kontrolü
    const minQuantity = getMinOrderQuantity(symbol);
    if (quantity < minQuantity) {
      return {
        isValid: false,
        reason: `Minimum işlem miktarı: ${minQuantity}`,
        adjustedQuantity: minQuantity
      };
    }

    // Minimum notional kontrolü
    const minNotional = getMinNotional(symbol);
    const notionalValue = quantity * price;
    if (notionalValue < minNotional) {
      const adjustedQuantity = minNotional / price;
      return {
        isValid: false,
        reason: `Minimum işlem değeri: ${minNotional} USDT`,
        adjustedQuantity: Math.ceil(adjustedQuantity * 1000) / 1000 // 3 decimal places
      };
    }

    // Precision kontrolü
    const quantityPrecision = getQuantityPrecision(symbol);
    const pricePrecision = getPricePrecision(symbol);

    const quantityStr = quantity.toFixed(quantityPrecision);
    const priceStr = price.toFixed(pricePrecision);

    console.log('✅ Trade validasyonu başarılı:', {
      symbol,
      quantity: parseFloat(quantityStr),
      price: parseFloat(priceStr),
      minQuantity,
      minNotional,
      notionalValue
    });

    return { isValid: true };

  } catch (error) {
    console.error('❌ Trade validasyon hatası:', error);
    return { isValid: true }; // Hata durumunda geç
  }
}

// Tüm desteklenen coin'leri listele
export function getSupportedCoins(): string[] {
  try {
    const exchangeInfo = tradingRulesData as any;

    if (!exchangeInfo.symbols || !Array.isArray(exchangeInfo.symbols)) {
      console.warn('⚠️ Trading rules dosyası geçersiz format');
      return ['BTC', 'ETH', 'BNB']; // Default listesi
    }

    const coins: string[] = [];

    exchangeInfo.symbols.forEach((symbolInfo: any) => {
      if (symbolInfo.symbol && symbolInfo.symbol.endsWith('USDT') && symbolInfo.status === 'TRADING') {
        const coin = symbolInfo.baseAsset;
        if (coin && !coins.includes(coin)) {
          coins.push(coin);
        }
      }
    });

    console.log('💎 Desteklenen coinler:', coins.slice(0, 10), `... (toplam ${coins.length})`);
    return coins.sort();

  } catch (error) {
    console.error('❌ Desteklenen coinleri listeleme hatası:', error);
    return ['BTC', 'ETH', 'BNB']; // Default listesi
  }
}

// Trading parametrelerini validate et (useRealTrading için)
export function validateTradeParameters(
  symbol: string,
  amount: number,
  leverage: number,
  currentPrice: number
): {
  isValid: boolean;
  reason?: string;
  adjustedAmount?: number;
} {
  console.log('🔍 Trading parametreleri validasyonu:', { symbol, amount, leverage, currentPrice });

  try {
    // 1. Temel kontroller
    if (amount <= 0) {
      return {
        isValid: false,
        reason: 'İşlem tutarı pozitif olmalıdır'
      };
    }

    if (leverage <= 0 || leverage > 125) {
      return {
        isValid: false,
        reason: 'Kaldıraç 1-125 arasında olmalıdır'
      };
    }

    if (currentPrice <= 0) {
      return {
        isValid: false,
        reason: 'Geçerli fiyat bilgisi gerekli'
      };
    }

    // 2. Symbol için trading kurallarını kontrol et
    const rules = getTradingRulesForSymbol(symbol);
    if (!rules) {
      console.warn('⚠️ Trading kuralları bulunamadı, basic validation ile devam ediliyor');
      return { isValid: true };
    }

    // 3. Maksimum kaldıraç kontrolü
    const maxLeverage = getMaxLeverage(symbol);
    if (leverage > maxLeverage) {
      return {
        isValid: false,
        reason: `Maksimum kaldıraç: ${maxLeverage}x`
      };
    }

    // 4. Minimum notional kontrolü
    const minNotional = getMinNotional(symbol);
    const totalValue = amount * leverage;
    if (totalValue < minNotional) {
      const adjustedAmount = minNotional / leverage;
      return {
        isValid: false,
        reason: `Minimum işlem değeri: ${minNotional} USDT (${adjustedAmount.toFixed(2)} USDT yatırım gerekli)`,
        adjustedAmount: Math.ceil(adjustedAmount * 100) / 100 // 2 decimal places
      };
    }

    // 5. Quantity precision kontrolü
    const quantity = totalValue / currentPrice;
    const minQuantity = getMinOrderQuantity(symbol);

    if (quantity < minQuantity) {
      // Minimum quantity için gereken tutar hesapla
      const requiredValue = minQuantity * currentPrice;
      const requiredAmount = requiredValue / leverage;

      return {
        isValid: false,
        reason: `Minimum işlem miktarı: ${minQuantity} ${extractCoinFromSymbol(symbol)} (${requiredAmount.toFixed(2)} USDT yatırım gerekli)`,
        adjustedAmount: Math.ceil(requiredAmount * 100) / 100
      };
    }

    console.log('✅ Trading parametreleri validasyonu başarılı:', {
      symbol,
      amount,
      leverage,
      currentPrice,
      totalValue,
      quantity,
      minNotional,
      minQuantity,
      maxLeverage
    });

    return { isValid: true };

  } catch (error) {
    console.error('❌ Trading parametreleri validasyon hatası:', error);
    return {
      isValid: false,
      reason: `Validasyon hatası: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`
    };
  }
}
