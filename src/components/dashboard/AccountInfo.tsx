import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Timer, RefreshCcw, Eye, EyeOff, Info } from "lucide-react";
import { useApiConnection } from "@/contexts/ApiConnectionContext";
import { Trade, TradeStats } from "@/types/trading";
import { useBinanceAccount } from "@/hooks/useBinanceAccount";
import { BinanceAuthCredentials } from "@/services/binanceService";
import { getWebSocketAPI } from "@/services/binanceWebSocketService";
import binanceApi from "@/services/binanceApi";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";

interface AccountInfoProps {
  balance: number;
  initialBalance: number;
  stats: TradeStats;
  tradingDuration: string | null;
  trades?: Trade[];
  onRefresh?: () => void;
  accountInfo?: any;
}

const AccountInfo: React.FC<AccountInfoProps> = ({
  balance,
  initialBalance,
  stats,
  tradingDuration,
  trades = [],
  onRefresh,
  accountInfo
}) => {
  const [showBalance, setShowBalance] = useState(false);
  const { isConnected, isRealTrading } = useApiConnection();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { accountInfo: hookAccountInfo, fetchAccountInfo } = useBinanceAccount();
  const [realAccountData, setRealAccountData] = useState<any>(null);

  const handleRefreshAccount = async () => {
    if (!isConnected) return;
    setIsRefreshing(true);
    try {
      onRefresh?.();
      const savedCredentialsJSON = localStorage.getItem('binance_credentials');
      if (savedCredentialsJSON) {
        const credentials: BinanceAuthCredentials = JSON.parse(savedCredentialsJSON);

        const restApi = binanceApi.getBinanceRestAPI(credentials);
        const accountData = await restApi.getAccountInfo();
        setRealAccountData(accountData);

        await fetchAccountInfo();
      }
    } catch (error) {
      console.error('❌ Error refreshing account data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    if (isConnected) {
      const saved = localStorage.getItem('binance_credentials');
      if (saved) {
        try {
          const credentials: BinanceAuthCredentials = JSON.parse(saved);

          const restApi = binanceApi.getBinanceRestAPI(credentials);
          restApi.getAccountInfo()
            .then(setRealAccountData)
            .catch((err) => {
              console.error('❌ Account data fetch failed:', err);
              setRealAccountData(null);
            });
        } catch (err) {
          console.error('❌ Error parsing saved credentials:', err);
        }
      } else {
        setRealAccountData(null);
      }
    } else {
      setRealAccountData(null);
    }
  }, [isConnected]);

  // Event listener'ları kurma
  useEffect(() => {
    console.log('🎧 AccountInfo event listener\'ları kuruluyor...');

    // Binance hesap güncellemeleri
    const handleAccountUpdate = (event: any) => {
      console.log('💰 AccountInfo: Hesap güncellemesi alındı:', event.detail);
      if (event.detail) {
        setRealAccountData(event.detail);
      }
    };

    // İlk yükleme için hesap bilgileri
    const handleInitialBalance = (event: any) => {
      console.log('🎯 AccountInfo: İlk bakiye bilgisi alındı:', event.detail);
      if (event.detail) {
        setRealAccountData(event.detail);
      }
    };

    // WebSocket pozisyon güncellemeleri
    const handlePositionUpdate = (event: any) => {
      console.log('📊 AccountInfo: Pozisyon güncellemesi alındı:', event.detail);
      // Pozisyon değişiklikleri hesap bilgilerini etkileyebilir
      // Hesap bilgilerini yeniden yükle
      if (isConnected) {
        console.log('🔄 Pozisyon güncellemesi nedeniyle hesap bilgileri yenileniyor...');
        // Bu durumda websocket'ten fresh data gelmeli
      }
    };

    // WebSocket emir güncellemeleri
    const handleOrderUpdate = (event: any) => {
      console.log('📈 AccountInfo: Emir güncellemesi alındı:', event.detail);
      // Emir durumu değişiklikleri hesap bilgilerini etkileyebilir
      if (event.detail && (event.detail.X === 'FILLED' || event.detail.X === 'PARTIALLY_FILLED')) {
        console.log('🔄 Emir gerçekleşmesi nedeniyle hesap bilgileri yenileniyor...');
        // Hesap bilgilerini yeniden yükle
      }
    };

    // Event listener'ları ekle
    window.addEventListener('binance-account-updated', handleAccountUpdate);
    window.addEventListener('initial-balance-loaded', handleInitialBalance);
    window.addEventListener('binance-positions-updated', handlePositionUpdate);
    window.addEventListener('binance-order-updated', handleOrderUpdate);

    // Cleanup function
    return () => {
      console.log('🧹 AccountInfo event listener\'ları temizleniyor...');
      window.removeEventListener('binance-account-updated', handleAccountUpdate);
      window.removeEventListener('initial-balance-loaded', handleInitialBalance);
      window.removeEventListener('binance-positions-updated', handlePositionUpdate);
      window.removeEventListener('binance-order-updated', handleOrderUpdate);
    };
  }, [isConnected]);

  // Gerçek zamanlı hesap güncellemeleri için isConnected dependency
  useEffect(() => {
    if (isConnected && isRealTrading) {
      console.log('🔄 WebSocket bağlantısı aktif, gerçek zamanlı güncellemeler başlıyor...');

      // WebSocket bağlantısı kurulduğunda hesap bilgilerini çek
      const fetchInitialData = async () => {
        try {
          console.log('📊 İlk hesap bilgileri çekiliyor...');
          // Bu işlem WebSocket service içinde otomatik olarak yapılıyor
        } catch (error) {
          console.error('❌ İlk hesap bilgileri çekme hatası:', error);
        }
      };

      fetchInitialData();
    }
  }, [isConnected, isRealTrading]);

  const calculateTradeStats = () => {
    if (!trades.length) return {
      totalTrades: 0,
      openTrades: 0,
      closedTrades: 0,
      profitableTrades: 0,
      lossTrades: 0,
      totalProfitAmount: 0,
      totalFees: 0,
      netProfitPercentage: 0
    };
    const open = trades.filter(t => t.status === 'open').length;
    const closed = trades.filter(t => t.status === 'closed');
    const profitable = closed.filter(t => (t.actualProfitAmount || 0) > 0).length;
    const loss = closed.filter(t => (t.actualProfitAmount || 0) < 0).length;
    const profitSum = closed.reduce((sum, t) => sum + (t.actualProfitAmount || 0), 0);
    const feeSum = trades.reduce((sum, t) => sum + (t.totalFees || t.commissionFee || 0), 0);

    // Calculate net profit percentage (profit after fees)
    const netProfit = profitSum - feeSum;
    const netProfitPercentage = initialBalance > 0 ? (netProfit / initialBalance) * 100 : 0;

    return {
      totalTrades: trades.length,
      openTrades: open,
      closedTrades: closed.length,
      profitableTrades: profitable,
      lossTrades: loss,
      totalProfitAmount: profitSum,
      totalFees: feeSum,
      netProfitPercentage
    };
  };

  const displayStats = (() => {
    // Always use calculateTradeStats for consistent trade statistics
    const baseStats = calculateTradeStats();

    if (!isConnected) return baseStats;

    // For real trading, we can enhance with real account data if needed
    // but keep the trade counts from our local trades array
    if (hookAccountInfo || accountInfo || realAccountData) {
      return {
        ...baseStats,
        // Keep all calculated stats from trades, but could add real account PnL if needed
      };
    }

    return baseStats;
  })();

  // Balance'ı doğru şekilde hesapla
  const displayBalance = (() => {
    // İlk olarak hook'tan gelen accountInfo (WebSocket'ten gelen gerçek veri)
    if (hookAccountInfo?.totalWalletBalance) {
      const balance = parseFloat(hookAccountInfo.totalWalletBalance);
      console.log('🏦 [AccountInfo] Using hookAccountInfo balance:', balance);
      return balance;
    }

    // Prop olarak gelen accountInfo (Dashboard'tan geçirilen)
    if (accountInfo?.totalWalletBalance) {
      const balance = parseFloat(accountInfo.totalWalletBalance);
      console.log('🏦 [AccountInfo] Using prop accountInfo balance:', balance);
      return balance;
    }

    if (realAccountData?.totalWalletBalance) {
      const balance = parseFloat(realAccountData.totalWalletBalance);
      console.log('🏦 [AccountInfo] Using realAccountData balance:', balance);
      return balance;
    }

    // Context'ten gelen balance'ı kullan (hem test hem gerçek mod için)
    if (balance > 0) {
      console.log('🏦 [AccountInfo] Using context balance:', balance);
      return balance;
    }

    console.warn('🏦 [AccountInfo] No balance data available, using 0');
    return 0;
  })();

  const commissionRates = {
    maker: 0.0010,
    taker: 0.0010,
    makerBNB: 0.00075,
    takerBNB: 0.00075
  };

  // Gerçek hesap verilerini almak için yardımcı fonksiyon
  const getDisplayValue = (field: string, defaultValue: string = '0.00') => {
    console.log(`🔍 [AccountInfo] getDisplayValue(${field})`);

    // Hook'tan gelen accountInfo (WebSocket'ten gelen gerçek veri) - 1. öncelik
    if (hookAccountInfo && hookAccountInfo[field] !== undefined && hookAccountInfo[field] !== null) {
      const value = parseFloat(hookAccountInfo[field]).toFixed(2);
      console.log(`✅ [AccountInfo] Using hookAccountInfo.${field}: ${value}`);
      return value;
    }

    // Prop olarak gelen accountInfo (Dashboard'tan geçirilen) - 2. öncelik  
    if (accountInfo && accountInfo[field] !== undefined && accountInfo[field] !== null) {
      const value = parseFloat(accountInfo[field]).toFixed(2);
      console.log(`✅ [AccountInfo] Using accountInfo.${field}: ${value}`);
      return value;
    }

    // Real account data - 3. öncelik (WebSocket'ten gelen custom event veri)
    if (realAccountData) {
      console.log('📊 [AccountInfo] realAccountData available:', Object.keys(realAccountData));

      let realValue = null;

      // Improved field mapping - Binance WebSocket API response format
      switch (field) {
        case 'availableBalance':
          // Binance WebSocket: cw (crossWalletBalance) veya wb (walletBalance) kullan
          realValue = realAccountData.availableBalance ||
            realAccountData.cw ||
            realAccountData.wb ||
            realAccountData.balance;
          break;
        case 'totalWalletBalance':
          // Binance WebSocket: wb (walletBalance) veya balance field
          realValue = realAccountData.totalWalletBalance ||
            realAccountData.wb ||
            realAccountData.balance;
          break;
        case 'totalUnrealizedProfit':
          // Binance WebSocket: up (unrealizedProfit) field
          realValue = realAccountData.totalUnrealizedProfit ||
            realAccountData.up ||
            '0.00';
          break;
        default:
          realValue = realAccountData[field];
          break;
      }

      if (realValue !== undefined && realValue !== null && realValue !== "ORDER" && realValue !== "") {
        const value = parseFloat(realValue).toFixed(2);
        console.log(`✅ [AccountInfo] Using realAccountData.${field} (mapped): ${value}`);
        return value;
      }
    }

    console.log(`❌ [AccountInfo] No valid data for ${field}, using default: ${defaultValue}`);
    return defaultValue;
  };

  return (
    <Card className="h-[280px] mb-4 bg-slate-50 border-slate-200 shadow-md">
      <CardHeader className="pb-1 pt-2">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <CardTitle className="text-md">Hesap</CardTitle>
            <div className="flex items-center gap-1">
              <div className={`h-3 w-3 rounded-full ${isConnected ? "bg-green-500" : "bg-gray-300"}`} title="Bağlantı Durumu" />
              <div className={`h-3 w-3 rounded-full ${isRealTrading && isConnected ? "bg-yellow-500" : "bg-gray-300"}`} title="Gerçek Hesap" />
            </div>
            {tradingDuration && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Timer className="h-4 w-4" />
                {tradingDuration}
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" onClick={() => setShowBalance(!showBalance)} className="h-8 w-8 p-0">
              {showBalance ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </Button>
            <Button variant="ghost" size="sm" onClick={handleRefreshAccount} className="h-8 w-8 p-0">
              <RefreshCcw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0 pb-2">
        <div className="text-xl font-bold mb-2">
          {showBalance ? `$${displayBalance.toFixed(2)}` : '• • • •'}
        </div>
        <div className="grid grid-cols-1 gap-y-1 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Kullanılabilir Bakiye:</span>
            <span className="font-medium">{getDisplayValue('availableBalance')} USDT</span>
          </div>

          <div className="flex justify-between">
            <span className="text-muted-foreground">Gerçekleşmemiş P&L:</span>
            <span className={`font-medium ${parseFloat(getDisplayValue('totalUnrealizedProfit', '0')) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              {getDisplayValue('totalUnrealizedProfit')} USDT
            </span>
          </div>

          <div className="flex justify-between">
            <span className="text-muted-foreground">Çekilebilir Tutar:</span>
            <span className="font-medium">{getDisplayValue('totalWalletBalance')} USDT</span>
          </div>
        </div>

        {/* Trade Statistics Section */}
        <div className="mt-3 pt-2 border-t border-slate-300">
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Toplam İşlem:</span>
              <span className="font-medium">{displayStats.totalTrades}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Açık:</span>
              <span className="font-medium text-blue-600">{displayStats.openTrades}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Karlı:</span>
              <span className="font-medium text-green-600">{displayStats.profitableTrades}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Zararlı:</span>
              <span className="font-medium text-red-600">{displayStats.lossTrades}</span>
            </div>
          </div>

          {/* Profit/Loss Percentage - only show if trading has started */}
          {tradingDuration && tradingDuration !== "00:00:00" && (
            <div className="mt-2 pt-2 border-t border-slate-200">
              <div className="flex justify-between items-center">
                <span className="text-xs text-muted-foreground">Kar/Zarar (Komisyon Sonrası):</span>
                <span className={`text-sm font-bold ${displayStats.netProfitPercentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {displayStats.netProfitPercentage >= 0 ? '+' : ''}{displayStats.netProfitPercentage.toFixed(2)}%
                </span>
              </div>
            </div>
          )}
        </div>

        <div className="mt-2 flex flex-wrap items-center text-xs gap-x-2 gap-y-1 justify-between">
          <Popover>
            <PopoverTrigger asChild>
              <button className="flex items-center gap-1 text-muted-foreground text-xs">
                <Info className="h-3 w-3" />
                Komisyon Oranları
              </button>
            </PopoverTrigger>
            <PopoverContent className="w-56 p-2">
              <div className="space-y-1">
                <div>Maker: %{commissionRates.maker.toFixed(4)}</div>
                <div>Taker: %{commissionRates.taker.toFixed(4)}</div>
                <div>Maker (BNB): %{commissionRates.makerBNB.toFixed(4)}</div>
                <div>Taker (BNB): %{commissionRates.takerBNB.toFixed(4)}</div>
                <div className="mt-2 pt-1 border-t">Toplam Ödenen: ${displayStats.totalFees.toFixed(6)}</div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </CardContent>
    </Card>
  );
};

export default AccountInfo;
