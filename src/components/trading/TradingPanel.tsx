import React, { useRef, useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertCircle, ArrowUpRight, ArrowDownRight } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Slider } from "@/components/ui/slider";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Trade } from "@/types/trading";
import { useToast } from "@/hooks/use-toast";

interface TradingPanelProps {
  isAutoTradingEnabled: boolean;
  trades: Trade[];
  tradingStatus: 'inactive' | 'connecting' | 'active' | 'error';
  onToggleAutoTrading: () => void;
  tradingDirection: 'long' | 'short';
  setTradingDirection: (direction: 'long' | 'short') => void;
  tradeUnit: number;
  setTradeUnit: (amount: number) => void;
  profitTarget: number;
  setProfitTarget: (target: number) => void;
  stopLoss: number;
  setStopLoss: (stop: number) => void;
  leverage: number;
  setLeverage: (leverage: number) => void;
  stopNewTrades: boolean;
  setStopNewTrades: (stop: boolean) => void;
}

interface ExtendedTradingPanelProps extends TradingPanelProps {
  emergencyStopLossPercent?: number;
  emergencyStopLossEnabled?: boolean;
}

const TradingPanel: React.FC<ExtendedTradingPanelProps> = ({
  isAutoTradingEnabled,
  trades,
  tradingStatus,
  onToggleAutoTrading,
  tradingDirection,
  setTradingDirection,
  tradeUnit,
  setTradeUnit,
  profitTarget,
  setProfitTarget,
  stopLoss,
  setStopLoss,
  leverage,
  setLeverage,
  stopNewTrades,
  setStopNewTrades,
  emergencyStopLossPercent = 15,
  emergencyStopLossEnabled = true,
}) => {
  const [showWarningDialog, setShowWarningDialog] = useState(false);
  const [stopLossOrdersEnabled, setStopLossOrdersEnabled] = useState(true);
  const warningTriggerRef = useRef(null);

  const getStatusColor = (status: 'inactive' | 'connecting' | 'active' | 'error') => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'connecting': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-muted';
    }
  };

  const getStatusText = (status: 'inactive' | 'connecting' | 'active' | 'error') => {
    switch (status) {
      case 'active': return 'Aktif';
      case 'connecting': return 'Bağlanıyor';
      case 'error': return 'Hata';
      default: return 'Pasif';
    }
  };

  const handleToggleAutoTrading = () => {
    if (isAutoTradingEnabled && trades.some(t => t.status === 'open')) {
      setShowWarningDialog(true);
      return;
    }
    onToggleAutoTrading();
  };

  return (
    <Card className="animate-scale-in">
      <CardHeader className="pb-2 pt-3">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-md">İşlem Ayarları</CardTitle>
            <CardDescription>
              İşlem parametreleri ve otomatik işlem durumu
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 py-2">
        {/* İşlem Yönü - Gizli (Sadece SHORT kullanılıyor) */}
        <div className="hidden">
          <div className="space-y-2">
            <Label>İşlem Yönü</Label>
            <div className="flex gap-2">
              <Button
                variant={tradingDirection === 'short' ? "destructive" : "outline"}
                size="sm"
                onClick={() => setTradingDirection('short')}
                className="flex items-center gap-1 flex-1"
                disabled={isAutoTradingEnabled}
              >
                <ArrowDownRight className="h-3.5 w-3.5" />
                Short
              </Button>
            </div>
            {isAutoTradingEnabled && (
              <p className="text-xs text-muted-foreground mt-1">
                Otomatik işlem modunda işlem yönü değiştirilemez
              </p>
            )}
          </div>
        </div>

        {/* İşlem Ayarları bölümü gizlendi - kullanıcı talebi üzerine */}
        {false && (
          <div className="space-y-4">
            <div className="bg-muted/50 p-4 rounded-lg">
              <p className="text-sm text-center text-muted-foreground">
                ⚙️ İşlem ayarları yukarıdaki "İşlem Ayarları" kartında yapılabilir
              </p>
            </div>

            <div className="flex items-center gap-2 mt-2">
              <input
                type="checkbox"
                id="stopLossEnabled"
                checked={stopLossOrdersEnabled}
                onChange={(e) => setStopLossOrdersEnabled(e.target.checked)}
                disabled={isAutoTradingEnabled}
                className="rounded"
              />
              <Label htmlFor="stopLossEnabled" className="text-sm">
                Stop-loss emirlerini otomatik yerleştir
              </Label>
            </div>
          </div>
        )}

        <AlertDialog>
          <AlertDialogTrigger asChild>
            <span ref={warningTriggerRef} className="hidden" />
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Dikkat: Açık İşlemler Var</AlertDialogTitle>
              <AlertDialogDescription>
                Otomatik işlem modunu durdurmak, mevcut açık işlemlerin kapatılmasına neden olacaktır.
                Devam etmek istiyor musunuz?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>İptal</AlertDialogCancel>
              <AlertDialogAction onClick={() => {
                onToggleAutoTrading();
                setShowWarningDialog(false);
              }}>
                Devam Et
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  );
};

export default TradingPanel;
