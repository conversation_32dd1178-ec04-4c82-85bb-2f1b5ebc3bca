import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ArrowUp, ArrowDown, ListEnd, Clock, Calendar, DollarSign, Coins } from "lucide-react";
import { Trade } from "@/types/trading";
import { 
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { cn } from "@/lib/utils";
import { format, isToday } from 'date-fns';
import { 
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell 
} from "@/components/ui/table";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { formatPrice, calculateTradeDetails } from '@/utils/tradingCalculations';

type TradeHistoryProps = {
  trades: Trade[];
};

const TRADES_PER_PAGE = 10;

const TradeHistory: React.FC<TradeHistoryProps> = ({ trades }) => {
  const [currentPage, setCurrentPage] = useState(1);

  // Sort closed trades by exitTime, newest first
  const closedTrades = trades
    .filter(trade => trade.status === 'closed')
    .sort((a, b) => {
      const dateA = a.exitTime ? new Date(a.exitTime).getTime() : 0;
      const dateB = b.exitTime ? new Date(b.exitTime).getTime() : 0;
      return dateB - dateA; // Newest first
    });

  // Özet istatistikleri hesapla
  const calculateSummaryStats = () => {
    if (closedTrades.length === 0) {
      return {
        totalTrades: 0,
        totalProfitLoss: 0,
        totalCommission: 0,
        profitableCount: 0,
        lossCount: 0,
        winRate: 0,
        avgProfitLoss: 0
      };
    }

    const totalProfitLoss = closedTrades.reduce((sum, trade) => sum + (trade.actualProfitAmount || trade.profitLoss || 0), 0);
    const totalCommission = closedTrades.reduce((sum, trade) => sum + (trade.totalFees || trade.commissionFee || 0), 0);
    const profitableCount = closedTrades.filter(trade => (trade.actualProfitAmount || trade.profitLoss || 0) > 0).length;
    const lossCount = closedTrades.filter(trade => (trade.actualProfitAmount || trade.profitLoss || 0) < 0).length;
    const winRate = closedTrades.length > 0 ? (profitableCount / closedTrades.length) * 100 : 0;
    const avgProfitLoss = totalProfitLoss / closedTrades.length;

    return {
      totalTrades: closedTrades.length,
      totalProfitLoss,
      totalCommission,
      profitableCount,
      lossCount,
      winRate,
      avgProfitLoss
    };
  };

  const summaryStats = calculateSummaryStats();
    
  const totalPages = Math.ceil(closedTrades.length / TRADES_PER_PAGE);
  
  const startIndex = (currentPage - 1) * TRADES_PER_PAGE;
  const displayedTrades = closedTrades.slice(startIndex, startIndex + TRADES_PER_PAGE);

  const calculateTotalStats = () => {
    // Calculate total actual profit/loss amount from ALL closed trades, not just displayed ones
    const totalProfitLoss = closedTrades.reduce((sum, trade) => sum + (trade.actualProfitAmount || 0), 0);
    
    // Calculate total fees
    const totalFees = closedTrades.reduce((sum, trade) => sum + (trade.totalFees || 0), 0);
    
    // Calculate average percentage profit/loss from ALL closed trades
    const totalPercentage = closedTrades.length > 0 
      ? closedTrades.reduce((sum, trade) => sum + (trade.profitLoss || 0), 0) / closedTrades.length 
      : 0;
    
    return {
      totalProfitLoss,
      totalPercentage,
      totalFees,
      netProfit: totalProfitLoss - totalFees
    };
  };

  const { totalProfitLoss, totalPercentage, totalFees, netProfit } = calculateTotalStats();

  // Ek istatistikler
  const { profitableCount, lossCount, winRate, avgProfitLoss } = summaryStats;

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(p => p - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(p => p + 1);
    }
  };
  
  // Format date for display
  const formatTradeDate = (date: Date | undefined) => {
    if (!date) return "-";
    
    if (isToday(date)) {
      return `Bugün, ${format(date, 'HH:mm:ss')}`;
    }
    
    return format(date, 'dd.MM.yyyy HH:mm:ss');
  };

  // Format commission with higher precision
  const formatCommission = (amount: number | undefined): string => {
    if (amount === undefined || amount === null) return "$0.00";
    return `$${amount.toFixed(6)}`;
  };

  // Check if buttons should be disabled
  const isPreviousDisabled = currentPage === 1;
  const isNextDisabled = currentPage === totalPages;

  return (
    <Card className="animate-fade-in mt-4">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <ListEnd className="h-5 w-5" />
          İşlem Geçmişi
        </CardTitle>
        <CardDescription>
          Kapatılan işlemlerin özeti (en yeni işlemler ilk gösterilir)
        </CardDescription>
      </CardHeader>
      <CardContent>
        {closedTrades.length === 0 ? (
          <p className="text-center py-4 text-muted-foreground">
            Henüz kapatılan işlem bulunmuyor.
          </p>
        ) : (
          <>
            <div className="overflow-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Sembol</TableHead>
                    <TableHead>Yön</TableHead>
                    <TableHead>Giriş</TableHead>
                    <TableHead>Çıkış</TableHead>
                    <TableHead>Tutar ($)</TableHead>
                    <TableHead>
                      <span className="flex items-center gap-1">
                        <Coins className="h-3 w-3" /> Miktar
                      </span>
                    </TableHead>
                    <TableHead>Kaldıraç</TableHead>
                    <TableHead>Kar/Zarar</TableHead>
                    <TableHead>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="flex items-center gap-1 cursor-default">
                              <Coins className="h-3 w-3" /> Komisyon
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Toplam işlem komisyonu</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableHead>
                    <TableHead>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" /> Tarih/Saat
                      </span>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {displayedTrades.map((trade) => {
                    // Calculate coin quantity and details
                    const tradeDetails = calculateTradeDetails(
                      trade.investmentAmount || 0,
                      trade.leverage || 1,
                      trade.entryPrice,
                      trade.symbol
                    );

                    return (
                      <TableRow key={trade.id} className="hoverable-row">
                        <TableCell>{trade.symbol}</TableCell>
                        <TableCell>
                          <Badge variant={trade.direction === 'long' ? "default" : "destructive"} className="capitalize">
                            {trade.direction === 'long' ? (
                              <span className="flex items-center gap-1">
                                <ArrowUp className="h-3 w-3" />
                                Long
                              </span>
                            ) : (
                              <span className="flex items-center gap-1">
                                <ArrowDown className="h-3 w-3" />
                                Short
                              </span>
                            )}
                          </Badge>
                        </TableCell>
                        <TableCell>${formatPrice(trade.entryPrice)}</TableCell>
                        <TableCell>${trade.exitPrice ? formatPrice(trade.exitPrice) : '-'}</TableCell>
                        <TableCell>${trade.investmentAmount?.toFixed(2)}</TableCell>
                        <TableCell>
                          <div className="text-xs">
                            <div className="font-medium">{tradeDetails.formattedQuantity}</div>
                            <div className="text-muted-foreground">{tradeDetails.coinSymbol}</div>
                          </div>
                        </TableCell>
                        <TableCell>{trade.leverage}x</TableCell>
                        <TableCell>
                          {trade.profitLoss !== undefined && trade.actualProfitAmount !== undefined && (
                            <div className={`text-sm font-medium ${
                              trade.profitLoss >= 0 ? 'text-green-500' : 'text-red-500'
                            }`}>
                              <div>{trade.profitLoss >= 0 ? '+' : ''}{trade.profitLoss.toFixed(2)}%</div>
                              <div className="text-xs">
                                {trade.actualProfitAmount >= 0 ? '+' : '-'}${Math.abs(trade.actualProfitAmount).toFixed(2)}
                              </div>
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="text-sm">
                                  {formatCommission(trade.totalFees)}
                                </span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="text-xs space-y-1">
                                  <p>Giriş: {formatCommission(trade.entryCommission)}</p>
                                  <p>Çıkış: {formatCommission(trade.exitCommission)}</p>
                                  {trade.fundingFee !== undefined && trade.fundingFee > 0 && (
                                    <p>Funding: {formatCommission(trade.fundingFee)}</p>
                                  )}
                                  <Separator />
                                  <p className="font-bold">Toplam: {formatCommission(trade.totalFees)}</p>
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                        <TableCell className="text-xs whitespace-nowrap">
                          <div className="flex flex-col gap-1">
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" /> Açılış:
                              {formatTradeDate(trade.openTime)}
                            </span>
                            {trade.exitTime && (
                              <span className="flex items-center gap-1">
                                <Clock className="h-3 w-3" /> Kapanış:
                                {formatTradeDate(trade.exitTime)}
                              </span>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>

            {totalPages > 1 && (
              <div className="mt-4">
                <Pagination>
                  <PaginationContent className="flex justify-center">
                    <PaginationItem>
                      <PaginationPrevious 
                        onClick={handlePreviousPage}
                        className={cn(isPreviousDisabled && "pointer-events-none opacity-50")}
                      />
                    </PaginationItem>
                    
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      // Calculate page numbers to show based on current page
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }
                      
                      return (
                        <PaginationItem key={pageNum}>
                          <PaginationLink
                            onClick={() => setCurrentPage(pageNum)}
                            isActive={currentPage === pageNum}
                          >
                            {pageNum}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}
                    
                    <PaginationItem>
                      <PaginationNext
                        onClick={handleNextPage}
                        className={cn(isNextDisabled && "pointer-events-none opacity-50")}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
            
            <Separator className="my-4" />
            
            <div className="space-y-3">
              {/* Temel İstatistikler */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Toplam İşlem</span>
                  <Badge variant="outline">{closedTrades.length}</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Kazanma Oranı</span>
                  <Badge variant={winRate >= 50 ? "default" : "destructive"}>
                    {winRate.toFixed(1)}%
                  </Badge>
                </div>
              </div>

              {/* Kar/Zarar Detayları */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-green-600">Karlı İşlemler</span>
                  <span className="font-medium">{profitableCount}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-red-600">Zararlı İşlemler</span>
                  <span className="font-medium">{lossCount}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">Ortalama Kar/Zarar</span>
                  <span className={`font-medium ${avgProfitLoss >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                    {avgProfitLoss >= 0 ? '+' : ''}${avgProfitLoss.toFixed(2)}
                  </span>
                </div>
              </div>

              <Separator />

              {/* Finansal Özet */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Toplam Kar/Zarar</span>
                  <div className={`text-right font-medium ${
                    totalProfitLoss >= 0 ? 'text-green-500' : 'text-red-500'
                  }`}>
                    <div>{totalPercentage >= 0 ? '+' : ''}{totalPercentage.toFixed(2)}%</div>
                    <div className="text-sm">{totalProfitLoss >= 0 ? '+' : ''}${totalProfitLoss.toFixed(2)}</div>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Toplam Komisyonlar</span>
                  <div className="text-right font-medium text-orange-500">
                    <div className="text-sm">-${totalFees.toFixed(2)}</div>
                  </div>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <span className="font-bold">Net Kar/Zarar</span>
                  <div className={`text-right font-bold ${
                    netProfit >= 0 ? 'text-green-500' : 'text-red-500'
                  }`}>
                    <div className="text-lg">{netProfit >= 0 ? '+' : ''}${netProfit.toFixed(2)}</div>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default TradeHistory;
