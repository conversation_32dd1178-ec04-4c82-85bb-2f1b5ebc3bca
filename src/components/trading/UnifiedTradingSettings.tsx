import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { ArrowDownRight, Settings } from 'lucide-react';

interface UnifiedTradingSettingsProps {
  // Kaldıraç
  leverage: number;
  setLeverage: (leverage: number) => void;
  
  // İşlem tutarı
  tradeUnit: number;
  setTradeUnit: (amount: number) => void;
  
  // Kar hedefi
  profitTarget: number;
  setProfitTarget: (target: number) => void;
  
  // Stop loss
  stopLoss: number;
  setStopLoss: (stop: number) => void;
  
  // Alarm eşik değeri
  alertPercentage: number;
  setAlertPercentage: (percentage: number) => void;
  
  // Hareketli ortalama
  movingAveragePoints: number;
  setMovingAveragePoints: (points: number) => void;

  // Maksimum işlem sayısı
  maxOpenPositions: number;
  setMaxOpenPositions: (max: number) => void;

  // Durum
  isAutoTradingEnabled: boolean;
}

const UnifiedTradingSettings: React.FC<UnifiedTradingSettingsProps> = ({
  leverage,
  setLeverage,
  tradeUnit,
  setTradeUnit,
  profitTarget,
  setProfitTarget,
  stopLoss,
  setStopLoss,
  alertPercentage,
  setAlertPercentage,
  movingAveragePoints,
  setMovingAveragePoints,
  maxOpenPositions,
  setMaxOpenPositions,
  isAutoTradingEnabled,
}) => {
  return (
    <Card className="animate-scale-in">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          <div>
            <CardTitle className="text-lg">İşlem Ayarları</CardTitle>
            <CardDescription>
              Tüm trading parametreleri tek yerde
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* 1. Kaldıraç */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Kaldıraç</Label>
          <div className="flex gap-2">
            {[1, 5, 10, 20, 25].map((leverageOption) => (
              <Button
                key={leverageOption}
                variant={leverage === leverageOption ? "default" : "outline"}
                size="sm"
                onClick={() => setLeverage(leverageOption)}
                className="flex-1"
                disabled={isAutoTradingEnabled}
              >
                {leverageOption}x
              </Button>
            ))}
          </div>
          <p className="text-xs text-muted-foreground">
            Varsayılan: 20x
          </p>
        </div>

        {/* 2. Maksimum İşlem Sayısı */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Maksimum İşlem Sayısı</Label>
          <div className="flex gap-2">
            {[1, 3, 5, 10, 20].map((maxOption) => (
              <Button
                key={maxOption}
                variant={maxOpenPositions === maxOption ? "default" : "outline"}
                size="sm"
                onClick={() => setMaxOpenPositions(maxOption)}
                className="flex-1"
                disabled={isAutoTradingEnabled}
              >
                {maxOption}
              </Button>
            ))}
          </div>
          <p className="text-xs text-muted-foreground">
            Aynı anda açık olabilecek maksimum işlem sayısı (varsayılan: 1)
          </p>
        </div>

        {/* 3. İşlem Tutarı */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">İşlem Tutarı (USDT)</Label>
          <Input
            type="number"
            value={tradeUnit}
            onChange={(e) => setTradeUnit(Number(e.target.value))}
            min={0.1}
            step={0.1}
            disabled={isAutoTradingEnabled}
            placeholder="Min: $0.1"
          />
          <p className="text-xs text-muted-foreground">
            Min: $0.1, Hassasiyet: $0.1
          </p>
        </div>

        {/* 4. Kar Hedefi */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Kar Hedefi (%)</Label>
          <div className="flex items-center gap-3">
            <Slider
              value={[profitTarget]}
              onValueChange={(value) => setProfitTarget(value[0])}
              min={0.1}
              max={5}
              step={0.1}
              disabled={isAutoTradingEnabled}
              className="flex-1"
            />
            <span className="font-mono bg-muted px-2 py-1 rounded text-sm min-w-[50px] text-center">
              {profitTarget}%
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            Min: 0.1%, Max: 5%, Hassasiyet: 0.1%
          </p>
        </div>

        {/* 5. Stop Loss */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Stop Loss (%)</Label>
          <div className="flex items-center gap-3">
            <Slider
              value={[stopLoss]}
              onValueChange={(value) => setStopLoss(value[0])}
              min={0.1}
              max={5}
              step={0.1}
              disabled={isAutoTradingEnabled}
              className="flex-1"
            />
            <span className="font-mono bg-muted px-2 py-1 rounded text-sm min-w-[50px] text-center">
              {stopLoss}%
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            Min: 0.1%, Max: 5%, Hassasiyet: 0.1%
          </p>
        </div>

        {/* 6. Alarm Eşik Değeri */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Alarm Eşik Değeri (%)</Label>
          <div className="flex items-center gap-3">
            <Slider
              value={[alertPercentage]}
              onValueChange={(value) => setAlertPercentage(value[0])}
              min={0.1}
              max={5}
              step={0.1}
              disabled={isAutoTradingEnabled}
              className="flex-1"
            />
            <span className="font-mono bg-muted px-2 py-1 rounded text-sm min-w-[50px] text-center">
              {alertPercentage}%
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            MA üzerindeki yüzde alarm tetikleyici (varsayılan: 0.5%)
          </p>
        </div>

        {/* 7. Hareketli Ortalama */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Hareketli Ortalama Noktaları</Label>
          <div className="flex items-center gap-3">
            <Slider
              value={[movingAveragePoints]}
              onValueChange={(value) => setMovingAveragePoints(value[0])}
              min={5}
              max={50}
              step={1}
              disabled={isAutoTradingEnabled}
              className="flex-1"
            />
            <span className="font-mono bg-muted px-2 py-1 rounded text-sm min-w-[50px] text-center">
              {movingAveragePoints}
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            Hareketli ortalama için kullanılan veri noktalarının sayısı (varsayılan: 10)
          </p>
        </div>

        {/* Durum Bilgisi */}
        {isAutoTradingEnabled && (
          <div className="bg-muted/50 p-3 rounded-lg">
            <p className="text-sm text-muted-foreground text-center">
              ⚠️ Otomatik işlem modunda ayarlar değiştirilemez
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default UnifiedTradingSettings;
