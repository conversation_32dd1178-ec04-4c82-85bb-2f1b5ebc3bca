import { BinanceAuthCredentials } from './binanceService';
import credentials from '../config/binance-credentials.json';
import { getPositionSide } from './positionModeManager';

// WebSocket endpoint
const BINANCE_WS_URL = 'wss://fstream.binance.com/ws';

// User Data Stream endpoint (REST API ile listenKey alınacak)
const USER_DATA_STREAM_URL = 'wss://fstream.binance.com/ws';

// REST API endpoint
const API_BASE_URL = '/api/binance';

// Default credentials
const DEFAULT_CREDENTIALS = {
    apiKey: credentials.apiKey || '',
    apiSecret: ''
};

// WebSocket connection manager for market data
export class BinanceWebSocketAPI {
    public ws: WebSocket | null = null;
    public isConnected: boolean = false;
    public isAuthenticated: boolean = false;
    public isAuthenticatedForUserData: boolean = false;
    private credentials: BinanceAuthCredentials;
    private messageHandlers: Map<string, (response: any) => void> = new Map();
    private pingInterval: NodeJS.Timeout | null = null;
    private connectionRetries: number = 0;
    private maxRetries: number = 5;
    private listenKey: string | null = null;
    private userDataStreamWs: WebSocket | null = null;
    private listenKeyKeepAliveInterval: NodeJS.Timeout | null = null;

    // Public callbacks
    public onAuthSuccess: (() => void) | null = null;
    public onConnectionClose: ((event: CloseEvent) => void) | null = null;
    public onConnectionError: ((event: Event) => void) | null = null;

    constructor(credentials: BinanceAuthCredentials) {
        this.credentials = credentials;
        console.log(`🌐 WebSocketAPI başlatıldı - Piyasa verileri için`);
    }

    // Create listenKey (REST API ile)
    private async createListenKey(): Promise<string> {
        try {
            // REST API ile listenKey al - proxy üzerinden
            const response = await fetch('/api/binance/fapi/v1/listenKey', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-MBX-APIKEY': this.credentials.apiKey
                    // apiSecret header'da gönderilmez!
                }
            });

            if (!response.ok) {
                throw new Error(`Listen key oluşturulamadı: ${response.status}`);
            }

            const data = await response.json();
            console.log('✅ User Data Stream Listen Key oluşturuldu');
            return data.listenKey;
        } catch (error) {
            console.error('❌ Listen key oluşturma hatası:', error);
            throw error;
        }
    }

    // Keep alive listenKey (REST API ile)
    private async keepAliveListenKey(): Promise<void> {
        if (!this.listenKey) return;

        try {
            const response = await fetch('/api/binance/fapi/v1/listenKey', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-MBX-APIKEY': this.credentials.apiKey
                },
                body: JSON.stringify({ listenKey: this.listenKey })
            });

            if (response.ok) {
                console.log('🔄 User Data Stream Listen Key yenilendi');
            } else {
                console.error('❌ Listen key yenileme hatası:', response.status);
                // Listen key geçersiz olabilir, yeni bir tane oluştur
                this.listenKey = await this.createListenKey();
                console.log('🔄 Yeni Listen Key oluşturuldu');
            }
        } catch (error) {
            console.error('❌ Listen key yenileme hatası:', error);
        }
    }

    // Connect to User Data Stream
    private async connectUserDataStream(): Promise<void> {
        try {
            if (!this.listenKey) {
                this.listenKey = await this.createListenKey();
            }

            const userDataStreamUrl = `${USER_DATA_STREAM_URL}/${this.listenKey}`;
            console.log('🔌 User Data Stream bağlantısı kuruluyor...');

            this.userDataStreamWs = new WebSocket(userDataStreamUrl);

            this.userDataStreamWs.onopen = () => {
                console.log('✅ User Data Stream WebSocket bağlantısı kuruldu');
                this.isAuthenticatedForUserData = true;
                this.isAuthenticated = true;

                // Listen key'i her 30 dakikada bir yenile
                this.listenKeyKeepAliveInterval = setInterval(() => {
                    this.keepAliveListenKey();
                }, 30 * 60 * 1000); // 30 dakika

                if (this.onAuthSuccess) this.onAuthSuccess();
            };

            this.userDataStreamWs.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    console.log('📨 User Data Stream mesajı alındı:', data);
                    this.handleUserDataStreamMessage(data);
                } catch (error) {
                    console.error('❌ User Data Stream mesaj ayrıştırma hatası:', error);
                }
            };

            this.userDataStreamWs.onclose = (event) => {
                console.log('🔌 User Data Stream WebSocket bağlantısı kapatıldı:', event.code, event.reason);
                this.isAuthenticatedForUserData = false;

                if (this.listenKeyKeepAliveInterval) {
                    clearInterval(this.listenKeyKeepAliveInterval);
                    this.listenKeyKeepAliveInterval = null;
                }

                // Otomatik yeniden bağlanma
                if (this.connectionRetries < this.maxRetries) {
                    setTimeout(() => {
                        this.connectionRetries++;
                        console.log(`🔄 User Data Stream yeniden bağlanma denemesi ${this.connectionRetries}/${this.maxRetries}`);
                        this.connectUserDataStream().catch(console.error);
                    }, 5000 * this.connectionRetries);
                }
            };

            this.userDataStreamWs.onerror = (error) => {
                console.error('❌ User Data Stream WebSocket hatası:', error);
                this.isAuthenticatedForUserData = false;
            };
        } catch (error) {
            console.error('❌ User Data Stream bağlantı hatası:', error);
            throw error;
        }
    }

    // Handle User Data Stream messages
    private handleUserDataStreamMessage(data: any) {
        if (data.e === 'ACCOUNT_UPDATE') {
            console.log('💰 Hesap güncelleme alındı:', data);
            this.handleAccountUpdate(data);

            // Custom event olarak gönder
            window.dispatchEvent(new CustomEvent('binance-account-v2-updated', {
                detail: data
            }));
        }

        if (data.e === 'ORDER_TRADE_UPDATE') {
            console.log('📈 Emir güncelleme alındı:', data);
            this.handleOrderUpdate(data);
        }

        if (data.e === 'ACCOUNT_CONFIG_UPDATE') {
            console.log('⚙️ Hesap konfigürasyon güncelleme alındı:', data);
            this.handleConfigUpdate(data);
        }

        if (data.e === 'MARGIN_CALL') {
            console.log('🚨 Margin Call alındı:', data);
            window.dispatchEvent(new CustomEvent('binance-margin-call', {
                detail: data
            }));
        }
    }

    // Connect to WebSocket API for market data
    async connect(): Promise<boolean> {
        try {
            // Eğer zaten bağlı ise yeni bağlantı kurma
            if (this.ws && this.isConnected && this.ws.readyState === WebSocket.OPEN) {
                console.log('✅ WebSocket zaten bağlı, yeni bağlantı kurulmayacak');
                return true;
            }

            // Mevcut bağlantıyı temizle
            if (this.ws) {
                this.ws.close();
                this.ws = null;
            }

            // Connection retry counter'ını reset et
            this.connectionRetries = 0;

            // Promise ile WebSocket bağlantısını yönet
            return new Promise((resolve, reject) => {
                // Public WebSocket bağlantısı (market data için)
                const url = BINANCE_WS_URL;
                console.log(`🔌 Binance Public WebSocket bağlantısı kuruluyor: ${url}`);

                this.ws = new WebSocket(url);

                // Connection timeout (30 saniye)
                const connectionTimeout = setTimeout(() => {
                    if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
                        this.ws.close();
                        reject(new Error('WebSocket bağlantı timeout'));
                    }
                }, 30000);

                this.ws.onopen = async () => {
                    clearTimeout(connectionTimeout);
                    console.log('✅ Public WebSocket bağlantısı kuruldu');
                    this.isConnected = true;
                    this.connectionRetries = 0;

                    try {
                        // Ping interval başlat
                        this.startPingInterval();

                        // User Data Stream'i başlat (hesap güncellemeleri için)
                        await this.connectUserDataStream();

                        console.log('🎉 WebSocket bağlantısı ve User Data Stream başarılı!');
                        resolve(true);
                    } catch (error) {
                        clearTimeout(connectionTimeout);
                        console.error('❌ User Data Stream başlatma hatası:', error);
                        // Public WebSocket bağlantısı kuruldu, User Data Stream hatası var
                        // Bu durumda hala piyasa verilerini alabiliriz
                        resolve(true);
                    }
                };

                this.ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.handleMessage(data);
                    } catch (error) {
                        console.error('❌ WebSocket mesaj ayrıştırma hatası:', error);
                    }
                };

                this.ws.onclose = (event) => {
                    clearTimeout(connectionTimeout);
                    console.log('🔌 Public WebSocket bağlantısı kapatıldı:', event.code, event.reason);
                    this.isConnected = false;

                    // Ping interval'ı durdur
                    this.stopPingInterval();

                    if (this.onConnectionClose) this.onConnectionClose(event);

                    // Retry logic - sadece ciddi disconnection'larda retry yap
                    if (this.connectionRetries < this.maxRetries && event.code !== 1000 && event.code !== 1001) {
                        const baseDelay = 2000; // 2 saniye
                        const retryDelay = Math.min(baseDelay + (this.connectionRetries * 1000), 10000); // Max 10 saniye

                        console.log(`🔄 WebSocket yeniden bağlanma denemesi ${this.connectionRetries + 1}/${this.maxRetries} (${retryDelay}ms sonra)`);

                        setTimeout(() => {
                            this.connectionRetries++;
                            this.connect().catch((error) => {
                                console.error(`❌ Retry ${this.connectionRetries} başarısız:`, error);
                            });
                        }, retryDelay);
                    } else if (this.connectionRetries >= this.maxRetries) {
                        console.error(`❌ Maximum retry sayısına ulaşıldı (${this.connectionRetries}/${this.maxRetries})`);
                        this.connectionRetries = 0; // Reset counter
                    }
                };

                this.ws.onerror = (event) => {
                    clearTimeout(connectionTimeout);
                    console.error('❌ Public WebSocket hatası:', event);
                    this.isConnected = false;

                    if (this.onConnectionError) this.onConnectionError(event);

                    // Error durumunda onclose event'i handle eder
                    reject(new Error('WebSocket bağlantı hatası'));
                };
            });
        } catch (error) {
            console.error('❌ WebSocket connect hatası:', error);
            throw error;
        }
    }

    // Handle incoming messages from public WebSocket
    private handleMessage(data: any) {
        // Market data mesajlarını işle
        try {
            // Ticker update
            if (data.e === 'ticker' || (Array.isArray(data) && data[0]?.e === 'ticker')) {
                // Ticker verilerini işle ve UI'a ilet
                window.dispatchEvent(new CustomEvent('market-ticker-update', {
                    detail: Array.isArray(data) ? data : [data]
                }));
                return;
            }

            // Kline update
            if (data.e === 'kline' || (Array.isArray(data) && data[0]?.e === 'kline')) {
                // Kline verilerini işle ve UI'a ilet
                window.dispatchEvent(new CustomEvent('market-kline-update', {
                    detail: Array.isArray(data) ? data : [data]
                }));
                return;
            }

            // Book ticker update
            if (data.e === 'bookTicker' || (Array.isArray(data) && data[0]?.e === 'bookTicker')) {
                // Book ticker verilerini işle ve UI'a ilet
                window.dispatchEvent(new CustomEvent('market-bookticker-update', {
                    detail: Array.isArray(data) ? data : [data]
                }));
                return;
            }

            // Mini ticker update
            if (data.e === '24hrMiniTicker' || (Array.isArray(data) && data[0]?.e === '24hrMiniTicker')) {
                // Mini ticker verilerini işle ve UI'a ilet
                window.dispatchEvent(new CustomEvent('market-miniticker-update', {
                    detail: Array.isArray(data) ? data : [data]
                }));
                return;
            }

            // Ping-pong
            if (data.ping) {
                this.sendPong(data.ping);
                return;
            }
        } catch (error) {
            console.error('❌ Mesaj işleme hatası:', error);
        }
    }

    // Send pong response to ping
    private sendPong(pingPayload: any) {
        if (this.ws && this.isConnected) {
            this.ws.send(JSON.stringify({ pong: pingPayload }));
            console.log('🏓 Ping\'e yanıt olarak pong gönderildi');
        }
    }

    // Start ping interval
    private startPingInterval() {
        if (this.pingInterval) {
            clearInterval(this.pingInterval);
        }

        // Bağlantı durumunu kontrol et
        this.pingInterval = setInterval(() => {
            if (this.ws && this.ws.readyState !== WebSocket.OPEN) {
                console.warn('⚠️ WebSocket bağlantısı kopmuş, yeniden bağlanılıyor...');
                this.connect().catch(console.error);
            }
        }, 60000); // 1 dakikada bir bağlantı kontrolü
    }

    // Stop ping interval
    private stopPingInterval() {
        if (this.pingInterval) {
            clearInterval(this.pingInterval);
            this.pingInterval = null;
        }
    }

    // Generate UUID for message IDs
    private generateUUID(): string {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    // Subscribe to market data streams
    public subscribeToMarketData(streams: string[]): Promise<boolean> {
        if (!this.ws || !this.isConnected) {
            return Promise.reject(new Error('WebSocket bağlantısı aktif değil'));
        }

        const id = this.generateUUID();
        const request = {
            method: 'SUBSCRIBE',
            params: streams,
            id
        };

        return new Promise((resolve, reject) => {
            try {
                // Timeout for response
                const timeoutId = setTimeout(() => {
                    if (this.messageHandlers.has(id)) {
                        this.messageHandlers.delete(id);
                        reject(new Error('Subscription timeout'));
                    }
                }, 10000);

                // Response handler
                this.messageHandlers.set(id, (response) => {
                    clearTimeout(timeoutId);
                    if (response.result === null) {
                        console.log(`✅ Başarıyla abone olundu: ${streams.join(', ')}`);
                        resolve(true);
                    } else {
                        console.error('❌ Subscription hatası:', response);
                        reject(new Error(`Subscription failed: ${JSON.stringify(response)}`));
                    }
                });

                // Send subscription request
                this.ws.send(JSON.stringify(request));
                console.log(`📤 Subscription isteği gönderildi: ${streams.join(', ')}`);
            } catch (error) {
                console.error('❌ Subscription hatası:', error);
                reject(error);
            }
        });
    }

    // Unsubscribe from market data streams
    public unsubscribeFromMarketData(streams: string[]): Promise<boolean> {
        if (!this.ws || !this.isConnected) {
            return Promise.reject(new Error('WebSocket bağlantısı aktif değil'));
        }

        const id = this.generateUUID();
        const request = {
            method: 'UNSUBSCRIBE',
            params: streams,
            id
        };

        return new Promise((resolve, reject) => {
            try {
                // Timeout for response
                const timeoutId = setTimeout(() => {
                    if (this.messageHandlers.has(id)) {
                        this.messageHandlers.delete(id);
                        reject(new Error('Unsubscription timeout'));
                    }
                }, 10000);

                // Response handler
                this.messageHandlers.set(id, (response) => {
                    clearTimeout(timeoutId);
                    if (response.result === null) {
                        console.log(`✅ Başarıyla abonelik iptal edildi: ${streams.join(', ')}`);
                        resolve(true);
                    } else {
                        console.error('❌ Unsubscription hatası:', response);
                        reject(new Error(`Unsubscription failed: ${JSON.stringify(response)}`));
                    }
                });

                // Send unsubscription request
                this.ws.send(JSON.stringify(request));
                console.log(`📤 Unsubscription isteği gönderildi: ${streams.join(', ')}`);
            } catch (error) {
                console.error('❌ Unsubscription hatası:', error);
                reject(error);
            }
        });
    }

    // Account update handler
    private handleAccountUpdate(data: any): void {
        if (data.a && data.a.B) {
            // Bakiye güncelleme
            const balances = data.a.B;
            console.log('💵 Bakiye güncellemesi:', balances);

            // USDT balance'ı bul
            const usdtBalance = balances.find((b: any) => b.a === 'USDT');

            let totalWalletBalance = "0";
            let availableBalance = "0";
            let totalUnrealizedProfit = "0";

            if (usdtBalance) {
                totalWalletBalance = usdtBalance.wb || "0";        // walletBalance
                availableBalance = usdtBalance.cw || "0";          // crossWalletBalance
                totalUnrealizedProfit = usdtBalance.up || "0";     // unrealizedProfit

                console.log('💰 USDT balance parsed from WebSocket:', {
                    totalWalletBalance,
                    availableBalance,
                    totalUnrealizedProfit
                });
            }

            // Custom event dispatch
            const balanceData = {
                totalWalletBalance,
                availableBalance,
                totalUnrealizedProfit,
                balances
            };

            window.dispatchEvent(new CustomEvent('binance-account-updated', {
                detail: balanceData
            }));
        }

        if (data.a && data.a.P) {
            // Pozisyon güncelleme
            const positions = data.a.P;
            this.handlePositionUpdates(positions);
        }
    }

    // Position update'lerini işle
    private handlePositionUpdates(positions: any[]): void {
        console.log('📊 POSITION UPDATES RECEIVED:', positions.length, 'positions');

        // Sadece açık pozisyonları filtrele (positionAmt != 0)
        const openPositions = positions.filter(pos => parseFloat(pos.pa) !== 0);

        console.log(`📊 OPEN POSITIONS: ${openPositions.length} açık pozisyon tespit edildi`);

        openPositions.forEach(pos => {
            console.log(`📍 OPEN POSITION: ${pos.s} - Amount: ${pos.pa}, Entry Price: ${pos.ep}, PNL: ${pos.up}`);
        });

        // Global olarak güncel pozisyonları paylaş
        (window as any).realPositions = openPositions;
        (window as any).realOpenPositionsCount = openPositions.length;
        (window as any).openPositionSymbols = openPositions.map(pos => pos.s);

        console.log(`🔢 GLOBAL POSITION UPDATE: Count=${openPositions.length}, Symbols=[${openPositions.map(pos => pos.s).join(', ')}]`);

        // Position update event'i gönder
        window.dispatchEvent(new CustomEvent('positions-updated', {
            detail: {
                openPositions,
                openCount: openPositions.length,
                symbols: openPositions.map(pos => pos.s)
            }
        }));
    }

    // Order update handler
    private handleOrderUpdate(data: any): void {
        if (data.o) {
            const orderData = data.o;

            // Sadece TP/SL emirleri için detaylı log
            if (orderData.o === 'TAKE_PROFIT_MARKET' || orderData.o === 'STOP_MARKET') {
                console.log('🎯 TP/SL ORDER UPDATE:', {
                    symbol: orderData.s,
                    type: orderData.o,
                    status: orderData.X,
                    timestamp: new Date().toISOString()
                });
            }

            // TP/SL emirlerinin durumunu kontrol et
            this.handleTPSLOrderUpdate(orderData);

            window.dispatchEvent(new CustomEvent('binance-order-updated', {
                detail: orderData
            }));
        }
    }

    // TP/SL emir güncellemelerini işle
    private handleTPSLOrderUpdate(orderData: any): void {
        const { s: symbol, X: status, o: orderType, i: orderId } = orderData;

        console.log(`🔍 ORDER TYPE CHECK: ${orderType} - Is TP/SL: ${orderType === 'TAKE_PROFIT_MARKET' || orderType === 'STOP_MARKET'}`);

        // Sadece TP/SL emirlerini kontrol et
        if (orderType === 'TAKE_PROFIT_MARKET' || orderType === 'STOP_MARKET') {
            console.log(`🎯 TP/SL EMİR GÜNCELLENDİ:`, {
                symbol,
                orderId,
                type: orderType,
                status,
                isTPSL: true,
                timestamp: new Date().toISOString()
            });

            // Emir tamamlandıysa (FILLED)
            if (status === 'FILLED') {
                console.log(`✅ ${orderType} EMRİ GERÇEKLEŞTİ: ${symbol} - Order ID: ${orderId}`);
                console.log(`📝 İşlem geçmişine ekleniyor...`);

                // İşlem geçmişine ekle
                this.addToTradeHistory(symbol, orderType, orderData);

                console.log(`📡 Position closed event gönderiliyor...`);
                // Pozisyon tamamen kapandı sinyali gönder
                window.dispatchEvent(new CustomEvent('position-closed-by-tpsl', {
                    detail: {
                        symbol,
                        orderType,
                        orderId,
                        orderData
                    }
                }));

                console.log(`✅ TP/SL işlemi tamamlandı: ${symbol}`);
            }

            // Emir iptal edildiyse
            if (status === 'CANCELED') {
                console.log(`❌ ${orderType} emri iptal edildi: ${symbol} - Order ID: ${orderId}`);
            }
        } else {
            // TP/SL olmayan emirler için de log
            console.log(`📊 NON-TP/SL ORDER: ${orderType} - ${symbol} - ${status}`);
        }
    }

    // Diğer TP/SL emirlerini iptal et
    private async cancelOtherTPSLOrders(symbol: string, executedOrderId: string, executedOrderType: string): Promise<void> {
        try {
            console.log(`🔄 ${symbol} için diğer TP/SL emirleri iptal ediliyor...`);

            // Açık emirleri al
            const openOrders = await this.getOpenOrders(symbol);

            // Diğer TP/SL emirlerini bul
            const otherTPSLOrders = openOrders.filter(order =>
                (order.type === 'TAKE_PROFIT_MARKET' || order.type === 'STOP_MARKET') &&
                order.orderId.toString() !== executedOrderId.toString()
            );

            console.log(`🔍 ${symbol} için ${otherTPSLOrders.length} adet diğer TP/SL emri bulundu`);

            // Diğer emirleri iptal et
            for (const order of otherTPSLOrders) {
                try {
                    await this.cancelOrder(symbol, order.orderId.toString());
                    console.log(`✅ ${symbol} için ${order.type} emri iptal edildi - Order ID: ${order.orderId}`);
                } catch (error) {
                    console.error(`❌ ${symbol} için ${order.type} emri iptal edilemedi:`, error);
                }
            }

        } catch (error) {
            console.error(`❌ ${symbol} için diğer TP/SL emirleri iptal edilirken hata:`, error);
        }
    }

    // İşlem geçmişine ekle - Detaylı trade record oluştur
    private addToTradeHistory(symbol: string, orderType: string, orderData: any): void {
        try {
            // Mevcut açık işlemi bul (Dashboard'dan)
            const openTrade = this.findOpenTradeForSymbol(symbol);

            if (!openTrade) {
                console.warn(`⚠️ ${symbol} için açık işlem bulunamadı, basit trade record oluşturuluyor`);
            }

            // Komisyon hesapla (Binance Futures: %0.02 maker, %0.04 taker)
            const quantity = parseFloat(orderData.q || '0');
            const exitPrice = parseFloat(orderData.ap || orderData.p || '0');
            const notionalValue = quantity * exitPrice;
            const exitCommission = notionalValue * 0.0004; // %0.04 taker fee

            // Detaylı trade record oluştur
            const tradeRecord = {
                id: `tpsl_${orderData.i}_${Date.now()}`,
                symbol,
                direction: openTrade?.direction || (orderData.S === 'BUY' ? 'long' : 'short'),
                entryPrice: openTrade?.entryPrice || parseFloat(orderData.ap || orderData.p || '0'),
                exitPrice: exitPrice,
                quantity: quantity,
                leverage: openTrade?.leverage || 20,
                investmentAmount: openTrade?.investmentAmount || 0.3,

                // Zaman bilgileri
                openTime: openTrade?.openTime || new Date(orderData.T || Date.now()),
                exitTime: new Date(orderData.T || Date.now()),

                // Finansal bilgiler
                pnl: parseFloat(orderData.rp || '0'), // Realized PnL
                actualProfitAmount: parseFloat(orderData.rp || '0'),

                // Komisyon bilgileri
                entryCommission: openTrade?.entryCommission || (notionalValue * 0.0002), // Tahmini giriş komisyonu
                exitCommission: exitCommission,
                commissionFee: (openTrade?.entryCommission || (notionalValue * 0.0002)) + exitCommission,
                fundingFee: 0, // Funding fee hesaplanabilir
                totalFees: (openTrade?.entryCommission || (notionalValue * 0.0002)) + exitCommission,

                // Durum bilgileri
                status: 'closed',
                closeReason: orderType === 'TAKE_PROFIT_MARKET' ? 'take_profit' : 'stop_loss',
                isAutomatic: true,
                timestamp: new Date(orderData.T || Date.now()).toISOString()
            };

            console.log(`📝 Detaylı işlem geçmişine ekleniyor:`, tradeRecord);

            // İşlem geçmişi event'i gönder
            window.dispatchEvent(new CustomEvent('trade-completed', {
                detail: tradeRecord
            }));

        } catch (error) {
            console.error('❌ İşlem geçmişine eklenirken hata:', error);
        }
    }

    // Açık işlemi bul (Dashboard'dan gelen trades listesi)
    private findOpenTradeForSymbol(symbol: string): any {
        try {
            // Global trades listesine erişim (eğer varsa)
            const globalTrades = (window as any).globalTrades;
            if (globalTrades && Array.isArray(globalTrades)) {
                return globalTrades.find(trade =>
                    trade.symbol === symbol && trade.status === 'open'
                );
            }
            return null;
        } catch (error) {
            console.error('❌ Açık işlem aranırken hata:', error);
            return null;
        }
    }

    // Config update handler
    private handleConfigUpdate(data: any): void {
        console.log('⚙️ Konfigürasyon güncellemesi:', data);

        window.dispatchEvent(new CustomEvent('binance-config-updated', {
            detail: data
        }));
    }

    // Disconnect from WebSocket
    public disconnect(): void {
        console.log('🔌 WebSocket bağlantıları kapatılıyor...');

        this.stopPingInterval();
        this.messageHandlers.clear();

        // User Data Stream bağlantısını kapat
        if (this.userDataStreamWs) {
            this.userDataStreamWs.close(1000, 'Kullanıcı tarafından kapatıldı');
            this.userDataStreamWs = null;
            console.log('🔌 User Data Stream WebSocket kapatıldı');
        }

        // Listen key keep-alive interval'ını temizle
        if (this.listenKeyKeepAliveInterval) {
            clearInterval(this.listenKeyKeepAliveInterval);
            this.listenKeyKeepAliveInterval = null;
        }

        // Public WebSocket bağlantısını kapat
        if (this.ws) {
            this.ws.close(1000, 'Kullanıcı tarafından kapatıldı');
            this.ws = null;
            console.log('🔌 Public WebSocket kapatıldı');
        }

        // Listen key'i temizle (isteğe bağlı - REST API ile silinebilir)
        if (this.listenKey) {
            this.deleteListenKey().catch(console.error);
            this.listenKey = null;
        }

        this.isConnected = false;
        this.isAuthenticatedForUserData = false;
        this.connectionRetries = 0;
    }

    // Delete listenKey via REST API
    private async deleteListenKey(): Promise<void> {
        if (!this.listenKey) return;

        try {
            const response = await fetch('/api/binance/fapi/v1/listenKey', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-MBX-APIKEY': this.credentials.apiKey
                },
                body: JSON.stringify({ listenKey: this.listenKey })
            });

            if (response.ok) {
                console.log('🗑️ User Data Stream Listen Key silindi');
            } else {
                console.warn('⚠️ Listen key silinemedi:', response.status);
            }
        } catch (error) {
            console.warn('⚠️ Listen key silme hatası:', error);
        }
    }

    // Public getter for credentials
    public getCredentials(): BinanceAuthCredentials {
        return { ...this.credentials };
    }

    // Reset connection state
    public resetConnectionState(): void {
        console.log('🔄 Connection state resetleniyor...');
        this.connectionRetries = 0;
        this.isConnected = false;
        this.isAuthenticatedForUserData = false;

        // Mevcut WebSocket bağlantılarını temizle
        if (this.ws) {
            this.ws.close(1000, 'Connection reset');
            this.ws = null;
        }

        if (this.userDataStreamWs) {
            this.userDataStreamWs.close(1000, 'Connection reset');
            this.userDataStreamWs = null;
        }

        this.messageHandlers.clear();
        console.log('✅ Connection state resetlendi');
    }

    /**
     * WebSocket API üzerinden işlem yapmaya çalışıldığında, REST API kullanılması gerektiğini hatırlat
     * @deprecated - Use REST API for trading operations
     */
    public async placeOrder(): Promise<any> {
        console.error('❌ WebSocket API üzerinden işlem yapılamaz. Lütfen REST API kullanın.');
        throw new Error('Trading operations should use REST API, not WebSocket API');
    }

    /**
     * REST API ile kaldıraç ayarla
     */
    public async setLeverage(symbol: string, leverage: number): Promise<any> {
        try {
            // Genel proxy endpoint'i kullan
            const url = `/api/binance/proxy`;

            console.log(`🔧 Kaldıraç ayarlanıyor: ${symbol} = ${leverage}x`);

            // API isteği gönder - Genel proxy endpoint'i üzerinden
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-MBX-APIKEY': this.credentials.apiKey
                },
                body: JSON.stringify({
                    endpoint: '/fapi/v1/leverage',
                    method: 'POST',
                    headers: {
                        'X-MBX-APIKEY': this.credentials.apiKey
                    },
                    params: {
                        symbol: symbol,
                        leverage: leverage,
                        timestamp: Date.now()
                    }
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`❌ Leverage ayarlama hatası (${response.status}): ${errorText}`);
                throw new Error(`Leverage setting failed: ${response.status} - ${errorText}`);
            }

            const responseData = await response.json();

            if (!responseData.success) {
                console.error(`❌ Leverage ayarlama yanıt hatası:`, responseData);
                throw new Error(`Leverage setting failed: ${JSON.stringify(responseData.result || {})}`);
            }

            console.log(`✅ Kaldıraç başarıyla ayarlandı: ${symbol} = ${leverage}x`);
            return responseData.result;
        } catch (error) {
            console.error('❌ REST API leverage setting error:', error);
            // Kaldıraç ayarlamada hata olsa bile işlem yapmayı deneyelim
            console.warn('⚠️ Kaldıraç ayarlama hatası oluştu, mevcut kaldıraç ile devam ediliyor');
            return { symbol, leverage, message: "Failed but continuing" };
        }
    }

    /**
     * REST API ile LONG pozisyon aç
     */
    public async openLongPosition(symbol: string, quantity: string, leverage?: number): Promise<any> {
        try {
            // Önce kaldıraç ayarla (opsiyonel)
            if (leverage) {
                try {
                    await this.setLeverage(symbol, leverage);
                } catch (leverageError) {
                    console.warn(`⚠️ ${symbol} için kaldıraç ayarlama hatası, işleme devam ediliyor:`, leverageError);
                    // Kaldıraç hatası işlemi durdurmaz
                }
            }

            // LONG pozisyon aç - Genel proxy üzerinden
            const url = `/api/binance/proxy`;

            console.log(`🔧 LONG pozisyon açılıyor: ${symbol}, miktar: ${quantity}`);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-MBX-APIKEY': this.credentials.apiKey
                },
                body: JSON.stringify({
                    endpoint: '/fapi/v1/order',
                    method: 'POST',
                    headers: {
                        'X-MBX-APIKEY': this.credentials.apiKey
                    },
                    params: {
                        symbol,
                        side: 'BUY',
                        type: 'MARKET',
                        quantity,
                        positionSide: getPositionSide('LONG'), // Dinamik position side
                        newOrderRespType: 'RESULT',
                        timestamp: Date.now()
                    }
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`❌ Long pozisyon açma hatası (${response.status}): ${errorText}`);
                throw new Error(`Order placement failed: ${response.status} - ${errorText}`);
            }

            const responseData = await response.json();

            if (!responseData.success) {
                console.error(`❌ Long pozisyon açma yanıt hatası:`, responseData);
                throw new Error(`Order placement failed: ${JSON.stringify(responseData.result || {})}`);
            }

            console.log(`✅ LONG pozisyon başarıyla açıldı: ${symbol}`);
            return responseData.result;
        } catch (error) {
            console.error('❌ REST API order placement error:', error);
            throw error;
        }
    }

    /**
     * REST API ile SHORT pozisyon aç
     */
    public async openShortPosition(symbol: string, quantity: string, leverage?: number): Promise<any> {
        try {
            // Önce kaldıraç ayarla (opsiyonel)
            if (leverage) {
                try {
                    await this.setLeverage(symbol, leverage);
                } catch (leverageError) {
                    console.warn(`⚠️ ${symbol} için kaldıraç ayarlama hatası, işleme devam ediliyor:`, leverageError);
                    // Kaldıraç hatası işlemi durdurmaz
                }
            }

            // SHORT pozisyon aç - Genel proxy üzerinden
            const url = `/api/binance/proxy`;

            console.log(`🔧 SHORT pozisyon açılıyor: ${symbol}, miktar: ${quantity}`);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-MBX-APIKEY': this.credentials.apiKey
                },
                body: JSON.stringify({
                    endpoint: '/fapi/v1/order',
                    method: 'POST',
                    headers: {
                        'X-MBX-APIKEY': this.credentials.apiKey
                    },
                    params: {
                        symbol,
                        side: 'SELL',
                        type: 'MARKET',
                        quantity,
                        positionSide: getPositionSide('SHORT'), // Dinamik position side
                        newOrderRespType: 'RESULT',
                        timestamp: Date.now()
                    }
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`❌ Short pozisyon açma hatası (${response.status}): ${errorText}`);
                throw new Error(`Order placement failed: ${response.status} - ${errorText}`);
            }

            const responseData = await response.json();

            if (!responseData.success) {
                console.error(`❌ Short pozisyon açma yanıt hatası:`, responseData);
                throw new Error(`Order placement failed: ${JSON.stringify(responseData.result || {})}`);
            }

            console.log(`✅ SHORT pozisyon başarıyla açıldı: ${symbol}`);
            return responseData.result;
        } catch (error) {
            console.error('❌ REST API order placement error:', error);
            throw error;
        }
    }

    /**
     * REST API ile pozisyon kapat
     */
    public async closePosition(symbol: string, positionSide: 'LONG' | 'SHORT'): Promise<any> {
        try {
            // Pozisyon bilgilerini al
            const positions = await this.getPositions(symbol);
            const position = positions.find(p =>
                p.symbol === symbol &&
                ((positionSide === 'LONG' && parseFloat(p.positionAmt || '0') > 0) ||
                    (positionSide === 'SHORT' && parseFloat(p.positionAmt || '0') < 0))
            );

            if (!position) {
                throw new Error(`No ${positionSide} position found for ${symbol}`);
            }

            // Pozisyonu kapat - Genel proxy üzerinden
            const url = `/api/binance/proxy`;
            const quantity = Math.abs(parseFloat(position.positionAmt || position.positionAmount || '0')).toString();
            const side = positionSide === 'LONG' ? 'SELL' : 'BUY';

            console.log(`🔧 ${positionSide} pozisyonu kapatılıyor: ${symbol}, miktar: ${quantity}`);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-MBX-APIKEY': this.credentials.apiKey
                },
                body: JSON.stringify({
                    endpoint: '/fapi/v1/order',
                    method: 'POST',
                    params: {
                        symbol,
                        side,
                        type: 'MARKET',
                        quantity,
                        positionSide: getPositionSide(positionSide), // Dinamik position side
                        reduceOnly: true,
                        newOrderRespType: 'RESULT',
                        timestamp: Date.now()
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`Position close failed: ${response.status}`);
            }

            const responseData = await response.json();

            if (!responseData.success) {
                throw new Error(`Position close failed: ${JSON.stringify(responseData.result || {})}`);
            }

            console.log(`✅ ${positionSide} pozisyonu başarıyla kapatıldı: ${symbol}`);
            return responseData.result;
        } catch (error) {
            console.error('❌ REST API position close error:', error);
            throw error;
        }
    }

    /**
     * REST API ile margin tipi ayarla
     */
    public async setMarginType(symbol: string, marginType: 'ISOLATED' | 'CROSSED'): Promise<any> {
        try {
            const url = `${API_BASE_URL}/fapi/v1/marginType`;
            const body = {
                symbol,
                marginType
            };

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-MBX-APIKEY': this.credentials.apiKey
                },
                body: JSON.stringify(body)
            });

            if (!response.ok) {
                throw new Error(`Margin type setting failed: ${response.status}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('❌ REST API margin type setting error:', error);
            throw error;
        }
    }

    /**
     * REST API ile pozisyonları al
     */
    public async getPositions(symbol?: string): Promise<any[]> {
        try {
            // Genel proxy endpoint'i kullan
            const url = `/api/binance/proxy`;

            console.log(`🔍 Pozisyonlar alınıyor${symbol ? `: ${symbol}` : ''}`);

            const requestBody: any = {
                endpoint: '/fapi/v2/account',
                method: 'GET',
                headers: {
                    'X-MBX-APIKEY': this.credentials.apiKey
                }
            };

            // Symbol parametresi varsa ekle
            if (symbol) {
                requestBody.params = { symbol };
            }

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-MBX-APIKEY': this.credentials.apiKey
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`Get positions failed: ${response.status}`);
            }

            const responseData = await response.json();

            if (!responseData.success) {
                throw new Error(`Get positions failed: ${JSON.stringify(responseData.result || {})}`);
            }

            // Hesap bilgilerinden pozisyonları al
            const accountInfo = responseData.result;
            const positions = accountInfo.positions || [];

            // Eğer belirli bir sembol için pozisyon aranıyorsa filtrele
            const filteredPositions = symbol
                ? positions.filter((p: any) => p.symbol === symbol)
                : positions;

            console.log(`✅ ${filteredPositions.length} pozisyon bulundu`);
            return filteredPositions;
        } catch (error) {
            console.error('❌ REST API get positions error:', error);
            throw error;
        }
    }

    /**
     * REST API ile Stop Loss emri ver
     */
    public async placeStopLossOrder(symbol: string, side: 'BUY' | 'SELL', quantity: string, stopPrice: string): Promise<any> {
        try {
            // Genel proxy endpoint'i kullan
            const url = `/api/binance/proxy`;

            console.log(`🔧 Stop Loss emri veriliyor: ${symbol}, fiyat: ${stopPrice}`);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-MBX-APIKEY': this.credentials.apiKey
                },
                body: JSON.stringify({
                    endpoint: '/fapi/v1/order',
                    method: 'POST',
                    headers: {
                        'X-MBX-APIKEY': this.credentials.apiKey
                    },
                    params: {
                        symbol,
                        side,
                        type: 'STOP_MARKET',
                        timeInForce: 'GTE_GTC', // Futures için GTE_GTC kullan
                        quantity,
                        stopPrice,
                        closePosition: 'true', // Pozisyonu kapat
                        workingType: 'MARK_PRICE', // Mark price'a göre çalış
                        newOrderRespType: 'RESULT',
                        timestamp: Date.now()
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`Stop Loss order placement failed: ${response.status}`);
            }

            const responseData = await response.json();

            if (!responseData.success) {
                throw new Error(`Stop Loss order failed: ${JSON.stringify(responseData.result || {})}`);
            }

            console.log(`✅ Stop Loss emri başarıyla verildi: ${symbol} için ${stopPrice} fiyatından`);
            return responseData.result;
        } catch (error) {
            console.error('❌ REST API Stop Loss order error:', error);
            throw error;
        }
    }



    /**
     * Binance'dan gerçek pozisyonları al
     */
    public async getRealPositions(): Promise<any[]> {
        try {
            const url = `/api/binance/proxy`;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-MBX-APIKEY': this.credentials.apiKey
                },
                body: JSON.stringify({
                    endpoint: '/fapi/v2/positionRisk',
                    method: 'GET',
                    headers: {
                        'X-MBX-APIKEY': this.credentials.apiKey
                    },
                    params: {
                        timestamp: Date.now()
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`Position query failed: ${response.status}`);
            }

            const positions = await response.json();

            // Sadece açık pozisyonları filtrele (positionAmt != 0)
            const openPositions = positions.filter((pos: any) => parseFloat(pos.positionAmt) !== 0);

            console.log(`📊 BINANCE REAL POSITIONS: ${openPositions.length} açık pozisyon bulundu`);

            return openPositions;

        } catch (error) {
            console.error('❌ Binance pozisyon sorgusu hatası:', error);
            return [];
        }
    }

    /**
     * Binance'dan işlem geçmişini al
     */
    public async getRealTradeHistory(limit: number = 100): Promise<any[]> {
        try {
            const url = `/api/binance/proxy`;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-MBX-APIKEY': this.credentials.apiKey
                },
                body: JSON.stringify({
                    endpoint: '/fapi/v1/userTrades',
                    method: 'GET',
                    headers: {
                        'X-MBX-APIKEY': this.credentials.apiKey
                    },
                    params: {
                        limit,
                        timestamp: Date.now()
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`Trade history query failed: ${response.status}`);
            }

            const trades = await response.json();

            console.log(`📊 BINANCE TRADE HISTORY: ${trades.length} işlem bulundu`);

            return trades;

        } catch (error) {
            console.error('❌ Binance işlem geçmişi sorgusu hatası:', error);
            return [];
        }
    }

    /**
     * REST API ile Take Profit emri ver (Fallback - OCO desteklenmezse)
     */
    public async placeTakeProfitOrder(symbol: string, side: 'BUY' | 'SELL', quantity: string, stopPrice: string): Promise<any> {
        try {
            // Genel proxy endpoint'i kullan
            const url = `/api/binance/proxy`;

            console.log(`🔧 Take Profit emri veriliyor: ${symbol}, fiyat: ${stopPrice}`);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-MBX-APIKEY': this.credentials.apiKey
                },
                body: JSON.stringify({
                    endpoint: '/fapi/v1/order',
                    method: 'POST',
                    headers: {
                        'X-MBX-APIKEY': this.credentials.apiKey
                    },
                    params: {
                        symbol,
                        side,
                        type: 'TAKE_PROFIT_MARKET',
                        timeInForce: 'GTE_GTC', // Futures için GTE_GTC kullan
                        quantity,
                        stopPrice,
                        closePosition: 'true', // Pozisyonu kapat
                        workingType: 'MARK_PRICE', // Mark price'a göre çalış
                        newOrderRespType: 'RESULT',
                        timestamp: Date.now()
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`Take Profit order placement failed: ${response.status}`);
            }

            const responseData = await response.json();

            if (!responseData.success) {
                throw new Error(`Take Profit order failed: ${JSON.stringify(responseData.result || {})}`);
            }

            console.log(`✅ Take Profit emri başarıyla verildi: ${symbol} için ${stopPrice} fiyatından`);
            return responseData.result;
        } catch (error) {
            console.error('❌ REST API Take Profit order error:', error);
            throw error;
        }
    }
}

// Singleton instance
let webSocketAPIInstance: BinanceWebSocketAPI | null = null;

export const getWebSocketAPI = (
    credentials: BinanceAuthCredentials = DEFAULT_CREDENTIALS
): BinanceWebSocketAPI => {
    // Check if we need to create a new instance
    if (!webSocketAPIInstance ||
        webSocketAPIInstance.getCredentials().apiKey !== credentials.apiKey) {

        // Kimlik bilgileri boşsa, localStorage'dan yükle
        let finalCredentials = credentials;

        if (!credentials.apiKey || credentials.apiKey === DEFAULT_CREDENTIALS.apiKey) {
            try {
                const savedCredentialsJSON = localStorage.getItem('binance_credentials');
                if (savedCredentialsJSON) {
                    const savedCreds = JSON.parse(savedCredentialsJSON);
                    if (savedCreds.apiKey) {
                        finalCredentials = savedCreds;
                        console.log('🔑 Kaydedilmiş API key kullanılıyor:', finalCredentials.apiKey.substring(0, 8) + '...');
                    }
                }
            } catch (error) {
                console.warn('⚠️ Kaydedilmiş credentials yüklenemedi:', error);
            }
        }

        console.log('🔑 API key kullanılıyor:', finalCredentials.apiKey ? finalCredentials.apiKey.substring(0, 8) + '...' : 'Boş');

        // Disconnect old instance if it exists
        if (webSocketAPIInstance) {
            console.log('🧹 Eski WebSocket instance temizleniyor');
            webSocketAPIInstance.disconnect();
        }

        // Create new instance
        const newInstance = new BinanceWebSocketAPI(finalCredentials);

        // Store the instance
        webSocketAPIInstance = newInstance;

        return newInstance;
    }

    // Return existing instance
    return webSocketAPIInstance;
};

export const cleanupWebSocketAPI = () => {
    if (webSocketAPIInstance) {
        webSocketAPIInstance.disconnect();
        webSocketAPIInstance = null;
    }
};

