/**
 * Position Mode Manager
 * Manages Binance position mode (One-way vs Hedge) and provides correct positionSide values
 */

export type PositionMode = 'ONE_WAY' | 'HEDGE';
export type PositionDirection = 'LONG' | 'SHORT';

class PositionModeManager {
    private currentMode: PositionMode = 'ONE_WAY'; // Default to One-way mode
    private isInitialized: boolean = false;

    /**
     * Initialize position mode by checking current account settings
     */
    async initialize(api: any): Promise<void> {
        try {
            console.log('🔍 Pozisyon modu kontrol ediliyor...');

            // Get current positions to determine mode
            const positions = await api.getPositions();

            // Check if we have LONG/SHORT positions (Hedge mode) or BOTH positions (One-way mode)
            const hasLongShortPositions = positions.some((pos: any) =>
                pos.positionSide === 'LONG' || pos.positionSide === 'SHORT'
            );

            this.currentMode = hasLongShortPositions ? 'HEDGE' : 'ONE_WAY';

            console.log(`📊 Pozisyon modu tespit edildi: ${this.currentMode}`);

            // Try to set to One-way mode for better compatibility
            if (this.currentMode === 'HEDGE') {
                try {
                    console.log('🔄 One-way mode\'a geçiliyor...');

                    // Check if API has setPositionMode method (REST API)
                    if (api.setPositionMode) {
                        await api.setPositionMode(false); // false = One-way mode
                    } else {
                        console.warn('⚠️ API setPositionMode metodunu desteklemiyor, manuel ayar gerekli');
                    }

                    this.currentMode = 'ONE_WAY';
                    console.log('✅ One-way mode\'a geçildi');
                } catch (error) {
                    console.warn('⚠️ One-way mode\'a geçilemedi, mevcut mod korunuyor:', error);
                    // Keep current mode if switching fails
                }
            }

            this.isInitialized = true;

        } catch (error) {
            console.error('❌ Pozisyon modu konfigürasyonu sırasında hata:', error);
            // Default to One-way mode on error
            this.currentMode = 'ONE_WAY';
            this.isInitialized = true;
        }
    }

    /**
     * Get the correct positionSide value for orders
     */
    getPositionSide(direction: PositionDirection): string {
        if (!this.isInitialized) {
            console.warn('⚠️ Position mode manager not initialized, using default ONE_WAY mode');
        }

        if (this.currentMode === 'HEDGE') {
            // Hedge mode: use LONG or SHORT
            return direction;
        } else {
            // One-way mode: always use BOTH
            return 'BOTH';
        }
    }

    /**
     * Get current position mode
     */
    getCurrentMode(): PositionMode {
        return this.currentMode;
    }

    /**
     * Check if position mode manager is initialized
     */
    isReady(): boolean {
        return this.isInitialized;
    }

    /**
     * Force set position mode (for testing or manual override)
     */
    setMode(mode: PositionMode): void {
        this.currentMode = mode;
        console.log(`🔧 Pozisyon modu manuel olarak ayarlandı: ${mode}`);
    }

    /**
     * Reset initialization state
     */
    reset(): void {
        this.isInitialized = false;
        this.currentMode = 'ONE_WAY';
    }
}

// Singleton instance
const positionModeManager = new PositionModeManager();

export default positionModeManager;

// Export convenience functions
export const initializePositionMode = (api: any) => positionModeManager.initialize(api);
export const getPositionSide = (direction: PositionDirection) => positionModeManager.getPositionSide(direction);
export const getCurrentPositionMode = () => positionModeManager.getCurrentMode();
export const isPositionModeReady = () => positionModeManager.isReady();
