// Test script to check SKATEUSDT precision from Binance API
const fetch = require('node-fetch');

async function testSKATEUSDTPrecision() {
    try {
        console.log('🔍 SKATEUSDT precision bilgileri getiriliyor...');
        
        const response = await fetch('https://fapi.binance.com/fapi/v1/exchangeInfo');
        const data = await response.json();
        
        const skateInfo = data.symbols.find(s => s.symbol === 'SKATEUSDT');
        
        if (skateInfo) {
            console.log('✅ SKATEUSDT bilgileri bulundu:');
            console.log('Symbol:', skateInfo.symbol);
            console.log('Status:', skateInfo.status);
            console.log('Base Asset:', skateInfo.baseAsset);
            console.log('Quote Asset:', skateInfo.quoteAsset);
            console.log('Quantity Precision:', skateInfo.quantityPrecision);
            console.log('Price Precision:', skateInfo.pricePrecision);
            
            console.log('\n📊 Filters:');
            skateInfo.filters.forEach(filter => {
                if (filter.filterType === 'LOT_SIZE') {
                    console.log('LOT_SIZE Filter:');
                    console.log('  - minQty:', filter.minQty);
                    console.log('  - maxQty:', filter.maxQty);
                    console.log('  - stepSize:', filter.stepSize);
                }
                if (filter.filterType === 'MIN_NOTIONAL') {
                    console.log('MIN_NOTIONAL Filter:');
                    console.log('  - notional:', filter.notional);
                }
                if (filter.filterType === 'PRICE_FILTER') {
                    console.log('PRICE_FILTER:');
                    console.log('  - minPrice:', filter.minPrice);
                    console.log('  - maxPrice:', filter.maxPrice);
                    console.log('  - tickSize:', filter.tickSize);
                }
            });
            
            // Precision hesaplama
            const lotSizeFilter = skateInfo.filters.find(f => f.filterType === 'LOT_SIZE');
            if (lotSizeFilter) {
                const stepSize = parseFloat(lotSizeFilter.stepSize);
                console.log('\n🎯 Hesaplanan precision bilgileri:');
                console.log('Step Size:', stepSize);
                
                // Precision'ı stepSize'dan hesapla
                let precision = 0;
                if (stepSize > 0) {
                    const stepSizeStr = stepSize.toString();
                    if (stepSizeStr.includes('.')) {
                        precision = stepSizeStr.split('.')[1].length;
                    }
                }
                console.log('Calculated Precision:', precision);
                
                // Test quantity hesaplama
                const testPrice = 0.26935;
                const testInvestment = 0.3;
                const testLeverage = 20;
                const totalValue = testInvestment * testLeverage;
                const theoreticalQuantity = totalValue / testPrice;
                
                console.log('\n🧮 Test hesaplama:');
                console.log('Price:', testPrice);
                console.log('Investment:', testInvestment);
                console.log('Leverage:', testLeverage);
                console.log('Total Value:', totalValue);
                console.log('Theoretical Quantity:', theoreticalQuantity);
                
                // Doğru yuvarlanmış miktar
                const roundedQuantity = Math.floor(theoreticalQuantity / stepSize) * stepSize;
                const finalQuantity = parseFloat(roundedQuantity.toFixed(precision));
                
                console.log('Rounded Quantity:', roundedQuantity);
                console.log('Final Quantity:', finalQuantity);
                console.log('Notional Value:', finalQuantity * testPrice);
            }
        } else {
            console.log('❌ SKATEUSDT bilgileri bulunamadı');
        }
        
    } catch (error) {
        console.error('❌ Hata:', error);
    }
}

testSKATEUSDTPrecision();
